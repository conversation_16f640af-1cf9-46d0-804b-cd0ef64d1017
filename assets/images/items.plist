<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,0},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,0},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,64},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Apple_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,64},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Bonus_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,128},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Brick_01_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,128},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Brick_02_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,192},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Bridge_01.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,192},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Bridge_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,256},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Bubble_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,256},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Chest_01_Locked_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,320},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Chest_01_Unlocked_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,320},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Chest_02_Locked_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,384},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Chest_02_Unlocked_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,384},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Coin_01_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,448},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Coin_02_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,448},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Coin_03_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,512},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Coin_04_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,512},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Coin_05_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,576},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Coin_06_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,576},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Decor_Brick_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,640},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Diamond_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,640},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Key_01_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,32}</string>
                <key>spriteSourceSize</key>
                <string>{64,32}</string>
                <key>textureRect</key>
                <string>{{0,1600},{64,32}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Key_02_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,32}</string>
                <key>spriteSourceSize</key>
                <string>{64,32}</string>
                <key>textureRect</key>
                <string>{{64,1600},{64,32}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Ladder_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,896},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Life_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,896},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Light_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,960},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Mushrooms_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,960},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Rock_05.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,1024},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Rock_06.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,1024},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Rock_07.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,1088},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Rock_08.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,1088},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Spikes-2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,1152},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Spikes.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,1152},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Star_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,1216},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Wooden_Barrel_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,1472},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Wooden_Box_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,1472},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>ice_Bonus_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,704},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>ice_Brick_01_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,704},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>ice_Brick_02_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,768},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>ice_Caramel_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,768},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>ice_Wooden_Barrel_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,832},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>ice_Wooden_Box_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,832},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>volcano_Bonus_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,1216},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>volcano_Brick01_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,1280},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>volcano_Brick02_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,1280},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>volcano_Star_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,1344},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>volcano_Stone_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,1344},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>volcano_WoodenBarrel_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,1408},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>volcano_WoodenBox_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,1408},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>冰锥2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,1536},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>火山锥2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,1536},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>items.png</string>
            <key>size</key>
            <string>{128,1632}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:3b5602f63200b37bcae7c6d9516de303:3e33c04403e41a37cfac7af2cd8d3a86:9b2c40378ce3448cae3ca053079d3001$</string>
            <key>textureFileName</key>
            <string>items.png</string>
        </dict>
    </dict>
</plist>
