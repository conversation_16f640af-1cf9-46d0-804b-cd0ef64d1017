<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>Bag.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,-5}</string>
                <key>spriteSize</key>
                <string>{128,118}</string>
                <key>spriteSourceSize</key>
                <string>{128,128}</string>
                <key>textureRect</key>
                <string>{{320,492},{128,118}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Decor_Mechanism.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,-58}</string>
                <key>spriteSize</key>
                <string>{466,364}</string>
                <key>spriteSourceSize</key>
                <string>{480,480}</string>
                <key>textureRect</key>
                <string>{{0,0},{466,364}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Decor_Rock_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,-3}</string>
                <key>spriteSize</key>
                <string>{128,58}</string>
                <key>spriteSourceSize</key>
                <string>{128,64}</string>
                <key>textureRect</key>
                <string>{{286,952},{128,58}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Ground-Additional_01.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{320,320}</string>
                <key>spriteSourceSize</key>
                <string>{320,320}</string>
                <key>textureRect</key>
                <string>{{0,364},{320,320}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Ground-Additional_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,-7}</string>
                <key>spriteSize</key>
                <string>{286,306}</string>
                <key>spriteSourceSize</key>
                <string>{320,320}</string>
                <key>textureRect</key>
                <string>{{0,684},{286,306}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Lantern.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{128,128}</string>
                <key>spriteSourceSize</key>
                <string>{128,128}</string>
                <key>textureRect</key>
                <string>{{320,364},{128,128}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Mushrooms.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,-15}</string>
                <key>spriteSize</key>
                <string>{128,98}</string>
                <key>spriteSourceSize</key>
                <string>{128,128}</string>
                <key>textureRect</key>
                <string>{{320,610},{128,98}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Rock_06.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,1}</string>
                <key>spriteSize</key>
                <string>{54,52}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{448,418},{54,52}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Rock_07.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{58,54}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{448,364},{58,54}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Sign_01.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{118,128}</string>
                <key>spriteSourceSize</key>
                <string>{128,128}</string>
                <key>textureRect</key>
                <string>{{124,990},{118,128}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Sign_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{124,128}</string>
                <key>spriteSourceSize</key>
                <string>{128,128}</string>
                <key>textureRect</key>
                <string>{{0,990},{124,128}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Sign_04.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{118,128}</string>
                <key>spriteSourceSize</key>
                <string>{128,128}</string>
                <key>textureRect</key>
                <string>{{242,1010},{118,128}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Sign_05.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{118,128}</string>
                <key>spriteSourceSize</key>
                <string>{128,128}</string>
                <key>textureRect</key>
                <string>{{360,1010},{118,128}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Statue.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,-6}</string>
                <key>spriteSize</key>
                <string>{216,244}</string>
                <key>spriteSourceSize</key>
                <string>{256,256}</string>
                <key>textureRect</key>
                <string>{{286,708},{216,244}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>town.png</string>
            <key>size</key>
            <string>{506,1138}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:c52e587e4de40af5d2d63c7742a663cd:c75a07a75d591e45a198bf8fa79535ab:8f2a9d3c98b0f261ba64a0b53eb9789a$</string>
            <key>textureFileName</key>
            <string>town.png</string>
        </dict>
    </dict>
</plist>
