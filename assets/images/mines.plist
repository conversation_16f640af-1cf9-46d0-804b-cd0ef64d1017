<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>14_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,169}</string>
                <key>spriteSourceSize</key>
                <string>{120,169}</string>
                <key>textureRect</key>
                <string>{{0,0},{120,169}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>16_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,137}</string>
                <key>spriteSourceSize</key>
                <string>{120,137}</string>
                <key>textureRect</key>
                <string>{{120,307},{120,137}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>17_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,117}</string>
                <key>spriteSourceSize</key>
                <string>{120,117}</string>
                <key>textureRect</key>
                <string>{{120,1189},{120,117}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>19_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,127}</string>
                <key>spriteSourceSize</key>
                <string>{120,127}</string>
                <key>textureRect</key>
                <string>{{120,573},{120,127}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>20_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,110}</string>
                <key>spriteSourceSize</key>
                <string>{120,110}</string>
                <key>textureRect</key>
                <string>{{120,1420},{120,110}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>22_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,127}</string>
                <key>spriteSourceSize</key>
                <string>{120,127}</string>
                <key>textureRect</key>
                <string>{{0,574},{120,127}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>24_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,100}</string>
                <key>spriteSourceSize</key>
                <string>{120,100}</string>
                <key>textureRect</key>
                <string>{{0,1745},{120,100}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>25_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,114}</string>
                <key>spriteSourceSize</key>
                <string>{120,114}</string>
                <key>textureRect</key>
                <string>{{0,1306},{120,114}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>26_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,143}</string>
                <key>spriteSourceSize</key>
                <string>{120,143}</string>
                <key>textureRect</key>
                <string>{{120,164},{120,143}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>27_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,129}</string>
                <key>spriteSourceSize</key>
                <string>{120,129}</string>
                <key>textureRect</key>
                <string>{{120,444},{120,129}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>28_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,103}</string>
                <key>spriteSourceSize</key>
                <string>{120,103}</string>
                <key>textureRect</key>
                <string>{{120,1640},{120,103}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>30_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,127}</string>
                <key>spriteSourceSize</key>
                <string>{120,127}</string>
                <key>textureRect</key>
                <string>{{120,700},{120,127}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>31_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,114}</string>
                <key>spriteSourceSize</key>
                <string>{120,114}</string>
                <key>textureRect</key>
                <string>{{120,1306},{120,114}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>32_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,127}</string>
                <key>spriteSourceSize</key>
                <string>{120,127}</string>
                <key>textureRect</key>
                <string>{{0,701},{120,127}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>34_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,116}</string>
                <key>spriteSourceSize</key>
                <string>{120,116}</string>
                <key>textureRect</key>
                <string>{{0,1190},{120,116}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>35_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,112}</string>
                <key>spriteSourceSize</key>
                <string>{120,112}</string>
                <key>textureRect</key>
                <string>{{0,1420},{120,112}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>36_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,106}</string>
                <key>spriteSourceSize</key>
                <string>{120,106}</string>
                <key>textureRect</key>
                <string>{{0,1639},{120,106}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>37_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,103}</string>
                <key>spriteSourceSize</key>
                <string>{120,103}</string>
                <key>textureRect</key>
                <string>{{120,1743},{120,103}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>38_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,119}</string>
                <key>spriteSourceSize</key>
                <string>{120,119}</string>
                <key>textureRect</key>
                <string>{{120,1070},{120,119}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>39_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,121}</string>
                <key>spriteSourceSize</key>
                <string>{120,121}</string>
                <key>textureRect</key>
                <string>{{120,949},{120,121}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>40_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,141}</string>
                <key>spriteSourceSize</key>
                <string>{120,141}</string>
                <key>textureRect</key>
                <string>{{0,169},{120,141}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>41_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,164}</string>
                <key>spriteSourceSize</key>
                <string>{120,164}</string>
                <key>textureRect</key>
                <string>{{120,0},{120,164}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>42_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,120}</string>
                <key>spriteSourceSize</key>
                <string>{120,120}</string>
                <key>textureRect</key>
                <string>{{0,950},{120,120}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>43_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,120}</string>
                <key>spriteSourceSize</key>
                <string>{120,120}</string>
                <key>textureRect</key>
                <string>{{0,1070},{120,120}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>44_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,122}</string>
                <key>spriteSourceSize</key>
                <string>{120,122}</string>
                <key>textureRect</key>
                <string>{{120,827},{120,122}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>45_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,110}</string>
                <key>spriteSourceSize</key>
                <string>{120,110}</string>
                <key>textureRect</key>
                <string>{{120,1530},{120,110}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>46_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,134}</string>
                <key>spriteSourceSize</key>
                <string>{120,134}</string>
                <key>textureRect</key>
                <string>{{0,310},{120,134}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>47_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,130}</string>
                <key>spriteSourceSize</key>
                <string>{120,130}</string>
                <key>textureRect</key>
                <string>{{0,444},{120,130}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>48_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,122}</string>
                <key>spriteSourceSize</key>
                <string>{120,122}</string>
                <key>textureRect</key>
                <string>{{0,828},{120,122}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>50_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,107}</string>
                <key>spriteSourceSize</key>
                <string>{120,107}</string>
                <key>textureRect</key>
                <string>{{0,1532},{120,107}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>mines.png</string>
            <key>size</key>
            <string>{240,1846}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:5ce71d7e5928b42302878b6d4d8cf9ff:20dd9d4bce062af7f1ef8c199925b9a3:4d23dfd28f7cbafa28e8e365ecb3b97f$</string>
            <key>textureFileName</key>
            <string>mines.png</string>
        </dict>
    </dict>
</plist>
