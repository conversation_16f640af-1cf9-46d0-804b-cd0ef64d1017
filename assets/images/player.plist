<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>Dying_006_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{150,121}</string>
                <key>spriteSourceSize</key>
                <string>{150,121}</string>
                <key>textureRect</key>
                <string>{{0,0},{150,121}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Dying_007_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{150,121}</string>
                <key>spriteSourceSize</key>
                <string>{150,121}</string>
                <key>textureRect</key>
                <string>{{150,0},{150,121}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Dying_008_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{150,121}</string>
                <key>spriteSourceSize</key>
                <string>{150,121}</string>
                <key>textureRect</key>
                <string>{{300,0},{150,121}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Dying_009_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{150,121}</string>
                <key>spriteSourceSize</key>
                <string>{150,121}</string>
                <key>textureRect</key>
                <string>{{450,0},{150,121}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Dying_010_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{150,121}</string>
                <key>spriteSourceSize</key>
                <string>{150,121}</string>
                <key>textureRect</key>
                <string>{{600,0},{150,121}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Dying_011_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{150,121}</string>
                <key>spriteSourceSize</key>
                <string>{150,121}</string>
                <key>textureRect</key>
                <string>{{750,0},{150,121}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>player.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{150,121}</string>
                <key>spriteSourceSize</key>
                <string>{150,121}</string>
                <key>textureRect</key>
                <string>{{900,0},{150,121}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>player.png</string>
            <key>size</key>
            <string>{1050,121}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:f41d5b7d7eee361ff3c178d426daebc6:3dd24d13b445e7a5d0c9d6ab4be63dce:6f6c59c3c6b7e98e5fb22e47122cb74a$</string>
            <key>textureFileName</key>
            <string>player.png</string>
        </dict>
    </dict>
</plist>
