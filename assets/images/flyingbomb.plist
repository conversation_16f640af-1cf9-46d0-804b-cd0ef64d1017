<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>__flying_bomb_pink_flying_000_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,110}</string>
                <key>spriteSourceSize</key>
                <string>{120,110}</string>
                <key>textureRect</key>
                <string>{{0,0},{120,110}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>__flying_bomb_pink_flying_001_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,110}</string>
                <key>spriteSourceSize</key>
                <string>{120,110}</string>
                <key>textureRect</key>
                <string>{{120,0},{120,110}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>__flying_bomb_pink_flying_002_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,110}</string>
                <key>spriteSourceSize</key>
                <string>{120,110}</string>
                <key>textureRect</key>
                <string>{{240,0},{120,110}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>__flying_bomb_pink_flying_003_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,110}</string>
                <key>spriteSourceSize</key>
                <string>{120,110}</string>
                <key>textureRect</key>
                <string>{{360,0},{120,110}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>__flying_bomb_pink_flying_004_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,110}</string>
                <key>spriteSourceSize</key>
                <string>{120,110}</string>
                <key>textureRect</key>
                <string>{{480,0},{120,110}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>__flying_bomb_pink_flying_005_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,110}</string>
                <key>spriteSourceSize</key>
                <string>{120,110}</string>
                <key>textureRect</key>
                <string>{{600,0},{120,110}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>__flying_bomb_pink_flying_006_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,110}</string>
                <key>spriteSourceSize</key>
                <string>{120,110}</string>
                <key>textureRect</key>
                <string>{{720,0},{120,110}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>__flying_bomb_pink_flying_007_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,110}</string>
                <key>spriteSourceSize</key>
                <string>{120,110}</string>
                <key>textureRect</key>
                <string>{{840,0},{120,110}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>__flying_bomb_pink_flying_008_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,110}</string>
                <key>spriteSourceSize</key>
                <string>{120,110}</string>
                <key>textureRect</key>
                <string>{{960,0},{120,110}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>__flying_bomb_pink_flying_009_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,110}</string>
                <key>spriteSourceSize</key>
                <string>{120,110}</string>
                <key>textureRect</key>
                <string>{{1080,0},{120,110}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>flyingbomb.png</string>
            <key>size</key>
            <string>{1200,110}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:be6ae590e847d10a6d4c06ae4b700b15:dd10e42b2c02653c9ff40ed818a9e47e:49d20564d40d81c6d3236a6df5e78458$</string>
            <key>textureFileName</key>
            <string>flyingbomb.png</string>
        </dict>
    </dict>
</plist>
