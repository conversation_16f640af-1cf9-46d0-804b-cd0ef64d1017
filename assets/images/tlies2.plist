<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>Cartoon Ancient Dungeon Game Tileset Bonus.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,0},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Ancient Dungeon Game Tileset Brick_01.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,64},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Ancient Dungeon Game Tileset Brick_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,128},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Ancient Dungeon Game Tileset Ground_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,192},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Ancient Dungeon Game Tileset Ground_04.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,256},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Ancient Dungeon Game Tileset Ground_06.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,320},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Ancient Dungeon Game Tileset Ground_08.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,384},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Ancient Dungeon Game Tileset Ground_09.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,448},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Ancient Dungeon Game Tileset Ground_10.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,0},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Ancient Dungeon Game Tileset Ground_11.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,64},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Ancient Dungeon Game Tileset Ground_12.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,128},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Ancient Dungeon Game Tileset Ground_13.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,192},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Badlands Game Tileset Bonus.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,256},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Badlands Game Tileset Brick_01.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,320},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Badlands Game Tileset Brick_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,384},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Badlands Game Tileset Ground_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,448},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Badlands Game Tileset Ground_04.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{128,0},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Badlands Game Tileset Ground_06.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{128,64},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Badlands Game Tileset Ground_08.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{128,128},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Badlands Game Tileset Ground_09.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{128,192},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Badlands Game Tileset Ground_10.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{128,256},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Badlands Game Tileset Ground_11.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{128,320},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Badlands Game Tileset Ground_12.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{128,384},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Badlands Game Tileset Ground_13.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{128,448},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Castle Game Tileset Bonus.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{192,0},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Castle Game Tileset Brick_01.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{192,64},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Castle Game Tileset Brick_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{192,128},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Castle Game Tileset Ground_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{192,192},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Castle Game Tileset Ground_04.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{192,256},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Castle Game Tileset Ground_06.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{192,320},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Castle Game Tileset Ground_08.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{192,384},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Castle Game Tileset Ground_09.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{192,448},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Castle Game Tileset Ground_10.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{256,0},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Castle Game Tileset Ground_11.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{256,64},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Castle Game Tileset Ground_12.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{256,128},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Castle Game Tileset Ground_13.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{256,192},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Desert  Village Game Tileset Ground_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{256,256},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Desert  Village Game Tileset Ground_04.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{256,320},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Desert  Village Game Tileset Ground_06.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{256,384},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Desert  Village Game Tileset Ground_09.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{256,448},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Desert  Village Game Tileset Ground_10.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{320,0},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Desert  Village Game Tileset Ground_11.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{320,64},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Desert  Village Game Tileset Ground_12.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{320,128},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Desert  Village Game Tileset Ground_13.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{320,192},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Desert Game Tileset Bonus.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{320,256},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Desert Game Tileset Brick_01.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{320,320},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Desert Game Tileset Brick_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{320,384},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Desert Game Tileset Ground_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{320,448},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Desert Game Tileset Ground_04.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{384,0},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Desert Game Tileset Ground_06.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{384,64},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Desert Game Tileset Ground_08.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{384,128},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Desert Game Tileset Ground_09.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{384,192},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Desert Game Tileset Ground_10.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{384,256},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Desert Game Tileset Ground_11.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{384,320},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Desert Game Tileset Ground_12.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{384,384},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Desert Game Tileset Ground_13.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{384,448},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Desert Village Game Tileset Ground_08.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{448,0},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Dungeon Game Tileset Bonus.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{448,64},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Dungeon Game Tileset Brick_01.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{448,128},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Dungeon Game Tileset Brick_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{448,192},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Dungeon Game Tileset Ground_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{448,256},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Dungeon Game Tileset Ground_04.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{448,320},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Dungeon Game Tileset Ground_06.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{448,384},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Dungeon Game Tileset Ground_08.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{448,448},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Dungeon Game Tileset Ground_09.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{512,0},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Dungeon Game Tileset Ground_10.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{512,64},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Dungeon Game Tileset Ground_11.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{512,128},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Dungeon Game Tileset Ground_12.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{512,192},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Dungeon Game Tileset Ground_13.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{512,256},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Inferno Game tileset Bonus.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{512,320},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Inferno Game tileset Brick_01.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{512,384},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Inferno Game tileset Brick_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{512,448},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Inferno Game tileset Ground_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{576,0},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Inferno Game tileset Ground_04.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{576,64},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Inferno Game tileset Ground_06.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{576,128},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Inferno Game tileset Ground_08.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{576,192},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Inferno Game tileset Ground_09.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{576,256},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Inferno Game tileset Ground_10.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{576,320},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Inferno Game tileset Ground_11.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{576,384},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Inferno Game tileset Ground_12.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{576,448},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Inferno Game tileset Ground_13.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{640,0},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Inferno Village Game Tileset Ground_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{640,64},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Inferno Village Game Tileset Ground_04.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{640,128},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Inferno Village Game Tileset Ground_06.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{640,192},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Inferno Village Game Tileset Ground_08.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{640,256},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Inferno Village Game Tileset Ground_09.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{640,320},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Inferno Village Game Tileset Ground_10.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{640,384},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Inferno Village Game Tileset Ground_11.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{640,448},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Inferno Village Game Tileset Ground_12.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{704,0},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Inferno Village Game Tileset Ground_13.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{704,64},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Jungle Game Tileset_Platformer - Bonus.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{704,128},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Jungle Game Tileset_Platformer - Brick 01.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{704,192},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Jungle Game Tileset_Platformer - Brick 02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{704,256},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Jungle Game Tileset_Platformer - Ground 02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{704,320},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Jungle Game Tileset_Platformer - Ground 04.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{704,384},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Jungle Game Tileset_Platformer - Ground 06.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{704,448},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Jungle Game Tileset_Platformer - Ground 08.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{768,0},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Jungle Game Tileset_Platformer - Ground 09.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{768,64},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Jungle Game Tileset_Platformer - Ground 10.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{768,128},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Jungle Game Tileset_Platformer - Ground 11.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{768,192},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Jungle Game Tileset_Platformer - Ground 12.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{768,256},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Jungle Game Tileset_Platformer - Ground 13.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{768,320},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Market Game Tileset Ground_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{768,384},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Market Game Tileset Ground_04.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{768,448},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Market Game Tileset Ground_06.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{832,0},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Market Game Tileset Ground_08.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{832,64},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Market Game Tileset Ground_09.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{832,128},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Market Game Tileset Ground_10.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{832,192},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Market Game Tileset Ground_11.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{832,256},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Market Game Tileset Ground_12.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{832,320},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Market Game Tileset Ground_13.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{832,384},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Medieval Ruins Game Tileset Bonus.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{832,448},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Medieval Ruins Game Tileset Brick_01.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{896,0},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Medieval Ruins Game Tileset Brick_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{896,64},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Medieval Ruins Game Tileset Ground_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{896,128},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Medieval Ruins Game Tileset Ground_04.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{896,192},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Medieval Ruins Game Tileset Ground_06.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{896,256},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Medieval Ruins Game Tileset Ground_08.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{896,320},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Medieval Ruins Game Tileset Ground_09.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{896,384},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Medieval Ruins Game Tileset Ground_10.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{896,448},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Medieval Ruins Game Tileset Ground_11.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{960,0},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Medieval Ruins Game Tileset Ground_12.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{960,64},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Medieval Ruins Game Tileset Ground_13.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{960,128},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Spooky Level Set_Platformer - Bonus.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{960,192},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Spooky Level Set_Platformer - Brick 01.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{960,256},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Spooky Level Set_Platformer - Brick 02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{960,320},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Spooky Level Set_Platformer - Ground 02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{960,384},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Spooky Level Set_Platformer - Ground 04.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{960,448},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Spooky Level Set_Platformer - Ground 06.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1024,0},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Spooky Level Set_Platformer - Ground 08.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1024,64},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Spooky Level Set_Platformer - Ground 09.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1024,128},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Spooky Level Set_Platformer - Ground 10.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1024,192},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Spooky Level Set_Platformer - Ground 11.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1024,256},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Spooky Level Set_Platformer - Ground 12.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1024,320},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Spooky Level Set_Platformer - Ground 13.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1024,384},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Village Tileset Ground_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1024,448},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Village Tileset Ground_04.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1088,0},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Village Tileset Ground_06.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1088,64},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Village Tileset Ground_08.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1088,128},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Village Tileset Ground_09.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1088,192},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Village Tileset Ground_10.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1088,256},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Village Tileset Ground_11.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1088,320},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Village Tileset Ground_12.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1088,384},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Village Tileset Ground_13.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1088,448},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Winter Settlement Game Tileset Ground_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1152,0},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Winter Settlement Game Tileset Ground_04.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1152,64},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Winter Settlement Game Tileset Ground_06.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1152,128},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Winter Settlement Game Tileset Ground_08.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1152,192},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Winter Settlement Game Tileset Ground_09.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1152,256},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Winter Settlement Game Tileset Ground_10.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1152,320},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Winter Settlement Game Tileset Ground_11.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1152,384},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Winter Settlement Game Tileset Ground_12.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1152,448},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Cartoon Winter Settlement Game Tileset Ground_13.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1216,0},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Volcano Level Set_Platformer - Bonus.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1216,64},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Volcano Level Set_Platformer - Brick 01.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1216,128},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Volcano Level Set_Platformer - Brick 02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1216,192},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Volcano Level Set_Platformer - Ground 013.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1280,256},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Volcano Level Set_Platformer - Ground 02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1216,256},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Volcano Level Set_Platformer - Ground 04.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1216,320},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Volcano Level Set_Platformer - Ground 06.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1216,384},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Volcano Level Set_Platformer - Ground 08.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1216,448},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Volcano Level Set_Platformer - Ground 09.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1280,0},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Volcano Level Set_Platformer - Ground 10.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1280,64},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Volcano Level Set_Platformer - Ground 11.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1280,128},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Volcano Level Set_Platformer - Ground 12.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{1280,192},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>tlies2.png</string>
            <key>size</key>
            <string>{1344,512}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:579391208073cf92af0cc5752d71d49c:5ca0d154975d1922158f6583a241ae6f:359f728a01873ae6a077e1ab50dee60d$</string>
            <key>textureFileName</key>
            <string>tlies2.png</string>
        </dict>
    </dict>
</plist>
