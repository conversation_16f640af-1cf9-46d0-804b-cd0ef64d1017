<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>01_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{300,285}</string>
                <key>spriteSourceSize</key>
                <string>{300,285}</string>
                <key>textureRect</key>
                <string>{{0,0},{300,285}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>02_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{300,285}</string>
                <key>spriteSourceSize</key>
                <string>{300,285}</string>
                <key>textureRect</key>
                <string>{{300,0},{300,285}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>03_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{300,285}</string>
                <key>spriteSourceSize</key>
                <string>{300,285}</string>
                <key>textureRect</key>
                <string>{{600,0},{300,285}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>04_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{300,285}</string>
                <key>spriteSourceSize</key>
                <string>{300,285}</string>
                <key>textureRect</key>
                <string>{{0,285},{300,285}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>05_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{300,285}</string>
                <key>spriteSourceSize</key>
                <string>{300,285}</string>
                <key>textureRect</key>
                <string>{{0,570},{300,285}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>06_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{300,285}</string>
                <key>spriteSourceSize</key>
                <string>{300,285}</string>
                <key>textureRect</key>
                <string>{{300,285},{300,285}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>07_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{300,285}</string>
                <key>spriteSourceSize</key>
                <string>{300,285}</string>
                <key>textureRect</key>
                <string>{{600,285},{300,285}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>08_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{300,285}</string>
                <key>spriteSourceSize</key>
                <string>{300,285}</string>
                <key>textureRect</key>
                <string>{{300,570},{300,285}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>09_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{300,285}</string>
                <key>spriteSourceSize</key>
                <string>{300,285}</string>
                <key>textureRect</key>
                <string>{{600,570},{300,285}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>explode04.png</string>
            <key>size</key>
            <string>{900,855}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:60538f1a084d1690c85f1b03c4508361:4e2ec636fcdc56f98a013f17b729e791:f3d9a090a9b4e5a7ed96aa6810339b0b$</string>
            <key>textureFileName</key>
            <string>explode04.png</string>
        </dict>
    </dict>
</plist>
