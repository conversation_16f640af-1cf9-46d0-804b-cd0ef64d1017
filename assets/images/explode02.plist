<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>Effect-fx01_0_s.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{200,170}</string>
                <key>spriteSourceSize</key>
                <string>{200,170}</string>
                <key>textureRect</key>
                <string>{{0,0},{200,170}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Effect-fx01_10_s.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{200,170}</string>
                <key>spriteSourceSize</key>
                <string>{200,170}</string>
                <key>textureRect</key>
                <string>{{600,170},{200,170}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Effect-fx01_11_s.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{200,170}</string>
                <key>spriteSourceSize</key>
                <string>{200,170}</string>
                <key>textureRect</key>
                <string>{{600,340},{200,170}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Effect-fx01_12_s.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{200,170}</string>
                <key>spriteSourceSize</key>
                <string>{200,170}</string>
                <key>textureRect</key>
                <string>{{800,0},{200,170}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Effect-fx01_13_s.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{200,170}</string>
                <key>spriteSourceSize</key>
                <string>{200,170}</string>
                <key>textureRect</key>
                <string>{{800,170},{200,170}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Effect-fx01_14_s.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{200,170}</string>
                <key>spriteSourceSize</key>
                <string>{200,170}</string>
                <key>textureRect</key>
                <string>{{800,340},{200,170}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Effect-fx01_15_s.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{200,170}</string>
                <key>spriteSourceSize</key>
                <string>{200,170}</string>
                <key>textureRect</key>
                <string>{{1000,0},{200,170}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Effect-fx01_16_s.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{200,170}</string>
                <key>spriteSourceSize</key>
                <string>{200,170}</string>
                <key>textureRect</key>
                <string>{{1000,170},{200,170}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Effect-fx01_17_s.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{200,170}</string>
                <key>spriteSourceSize</key>
                <string>{200,170}</string>
                <key>textureRect</key>
                <string>{{1000,340},{200,170}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Effect-fx01_1_s.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{200,170}</string>
                <key>spriteSourceSize</key>
                <string>{200,170}</string>
                <key>textureRect</key>
                <string>{{0,170},{200,170}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Effect-fx01_2_s.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{200,170}</string>
                <key>spriteSourceSize</key>
                <string>{200,170}</string>
                <key>textureRect</key>
                <string>{{0,340},{200,170}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Effect-fx01_3_s.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{200,170}</string>
                <key>spriteSourceSize</key>
                <string>{200,170}</string>
                <key>textureRect</key>
                <string>{{200,0},{200,170}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Effect-fx01_4_s.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{200,170}</string>
                <key>spriteSourceSize</key>
                <string>{200,170}</string>
                <key>textureRect</key>
                <string>{{200,170},{200,170}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Effect-fx01_5_s.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{200,170}</string>
                <key>spriteSourceSize</key>
                <string>{200,170}</string>
                <key>textureRect</key>
                <string>{{200,340},{200,170}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Effect-fx01_6_s.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{200,170}</string>
                <key>spriteSourceSize</key>
                <string>{200,170}</string>
                <key>textureRect</key>
                <string>{{400,0},{200,170}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Effect-fx01_7_s.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{200,170}</string>
                <key>spriteSourceSize</key>
                <string>{200,170}</string>
                <key>textureRect</key>
                <string>{{400,170},{200,170}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Effect-fx01_8_s.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{200,170}</string>
                <key>spriteSourceSize</key>
                <string>{200,170}</string>
                <key>textureRect</key>
                <string>{{400,340},{200,170}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Effect-fx01_9_s.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{200,170}</string>
                <key>spriteSourceSize</key>
                <string>{200,170}</string>
                <key>textureRect</key>
                <string>{{600,0},{200,170}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>explode02.png</string>
            <key>size</key>
            <string>{1200,510}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:3e750940889e20b0d5ea4a7bed9bdcde:d425f53cf7fd2893aefb3a4d75df0415:87b9ca85099c3daaa2aea6ca452e8462$</string>
            <key>textureFileName</key>
            <string>explode02.png</string>
        </dict>
    </dict>
</plist>
