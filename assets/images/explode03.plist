<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>01.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{149,149}</string>
                <key>spriteSourceSize</key>
                <string>{149,149}</string>
                <key>textureRect</key>
                <string>{{0,0},{149,149}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{149,149}</string>
                <key>spriteSourceSize</key>
                <string>{149,149}</string>
                <key>textureRect</key>
                <string>{{149,0},{149,149}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>03.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{149,149}</string>
                <key>spriteSourceSize</key>
                <string>{149,149}</string>
                <key>textureRect</key>
                <string>{{298,0},{149,149}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>04.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{149,149}</string>
                <key>spriteSourceSize</key>
                <string>{149,149}</string>
                <key>textureRect</key>
                <string>{{447,0},{149,149}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>explode03.png</string>
            <key>size</key>
            <string>{596,149}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:1c2edbb45b7f4296623cb4a708e95998:5186bab0a683e7d8cd048353ac014dc4:4ea802f1d1117b218fd53bb7197f3177$</string>
            <key>textureFileName</key>
            <string>explode03.png</string>
        </dict>
    </dict>
</plist>
