<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>mining_effect_01.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{274,361}</string>
                <key>spriteSourceSize</key>
                <string>{274,361}</string>
                <key>textureRect</key>
                <string>{{0,0},{274,361}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>mining_effect_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{274,361}</string>
                <key>spriteSourceSize</key>
                <string>{274,361}</string>
                <key>textureRect</key>
                <string>{{0,361},{274,361}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>mining_effect_03.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{274,361}</string>
                <key>spriteSourceSize</key>
                <string>{274,361}</string>
                <key>textureRect</key>
                <string>{{274,0},{274,361}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>mining_effect_04.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{274,361}</string>
                <key>spriteSourceSize</key>
                <string>{274,361}</string>
                <key>textureRect</key>
                <string>{{274,361},{274,361}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>mining_effect_05.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{274,361}</string>
                <key>spriteSourceSize</key>
                <string>{274,361}</string>
                <key>textureRect</key>
                <string>{{548,0},{274,361}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>mining_effect_06.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{274,361}</string>
                <key>spriteSourceSize</key>
                <string>{274,361}</string>
                <key>textureRect</key>
                <string>{{548,361},{274,361}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>mining_effect_07.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{274,361}</string>
                <key>spriteSourceSize</key>
                <string>{274,361}</string>
                <key>textureRect</key>
                <string>{{822,0},{274,361}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>mining_effect_08.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{274,361}</string>
                <key>spriteSourceSize</key>
                <string>{274,361}</string>
                <key>textureRect</key>
                <string>{{822,361},{274,361}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>dig_fx.png</string>
            <key>size</key>
            <string>{1096,722}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:632b03b203cbdc1bf867d3fe61d82b0f:9c557d835891383c9861ec4244bc18eb:1ff6fb7520ced885a9a45e2837eb3066$</string>
            <key>textureFileName</key>
            <string>dig_fx.png</string>
        </dict>
    </dict>
</plist>
