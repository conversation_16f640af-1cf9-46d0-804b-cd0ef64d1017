<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>bomb_one_lit_01_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,283}</string>
                <key>spriteSourceSize</key>
                <string>{120,283}</string>
                <key>textureRect</key>
                <string>{{0,0},{120,283}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bomb_one_lit_02_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,283}</string>
                <key>spriteSourceSize</key>
                <string>{120,283}</string>
                <key>textureRect</key>
                <string>{{0,283},{120,283}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bomb_one_lit_03_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,283}</string>
                <key>spriteSourceSize</key>
                <string>{120,283}</string>
                <key>textureRect</key>
                <string>{{0,566},{120,283}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bomb_one_lit_04_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,283}</string>
                <key>spriteSourceSize</key>
                <string>{120,283}</string>
                <key>textureRect</key>
                <string>{{0,849},{120,283}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>grenade.png</string>
            <key>size</key>
            <string>{120,1132}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:1e99d5eb01e2576857f336b0c4e62ce8:2a58521b5e2b9765f9a1f4effdb8e908:617bed96ad6701c7568a2867f66ed07c$</string>
            <key>textureFileName</key>
            <string>grenade.png</string>
        </dict>
    </dict>
</plist>
