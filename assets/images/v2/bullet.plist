<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>Exhaust_1_008_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{32,89}</string>
                <key>spriteSourceSize</key>
                <string>{32,89}</string>
                <key>textureRect</key>
                <string>{{0,0},{32,89}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Exhaust_2_002_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{32,86}</string>
                <key>spriteSourceSize</key>
                <string>{32,86}</string>
                <key>textureRect</key>
                <string>{{0,89},{32,86}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Exhaust_3_005_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{32,53}</string>
                <key>spriteSourceSize</key>
                <string>{32,53}</string>
                <key>textureRect</key>
                <string>{{0,311},{32,53}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Exhaust_4_006_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{32,36}</string>
                <key>spriteSourceSize</key>
                <string>{32,36}</string>
                <key>textureRect</key>
                <string>{{0,364},{32,36}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Exhaust_5_000_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{32,67}</string>
                <key>spriteSourceSize</key>
                <string>{32,67}</string>
                <key>textureRect</key>
                <string>{{0,244},{32,67}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Fire_Shot_1_4_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{32,28}</string>
                <key>spriteSourceSize</key>
                <string>{32,28}</string>
                <key>textureRect</key>
                <string>{{0,587},{32,28}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Fire_Shot_3_2_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{32,69}</string>
                <key>spriteSourceSize</key>
                <string>{32,69}</string>
                <key>textureRect</key>
                <string>{{0,175},{32,69}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Fire_Shot_3_4_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{32,28}</string>
                <key>spriteSourceSize</key>
                <string>{32,28}</string>
                <key>textureRect</key>
                <string>{{0,615},{32,28}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Fire_Shot_4_1_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{32,30}</string>
                <key>spriteSourceSize</key>
                <string>{32,30}</string>
                <key>textureRect</key>
                <string>{{0,527},{32,30}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Fire_Shot_4_3_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{32,32}</string>
                <key>spriteSourceSize</key>
                <string>{32,32}</string>
                <key>textureRect</key>
                <string>{{0,400},{32,32}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Fire_Shot_4_4_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{32,30}</string>
                <key>spriteSourceSize</key>
                <string>{32,30}</string>
                <key>textureRect</key>
                <string>{{0,557},{32,30}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Fire_Shot_5_4_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{32,31}</string>
                <key>spriteSourceSize</key>
                <string>{32,31}</string>
                <key>textureRect</key>
                <string>{{0,496},{32,31}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Laser_1_4_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{32,32}</string>
                <key>spriteSourceSize</key>
                <string>{32,32}</string>
                <key>textureRect</key>
                <string>{{0,432},{32,32}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Laser_3_4_small.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{32,32}</string>
                <key>spriteSourceSize</key>
                <string>{32,32}</string>
                <key>textureRect</key>
                <string>{{0,464},{32,32}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>bullet.png</string>
            <key>size</key>
            <string>{32,643}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:133769020fc313821055fc9f0fa9ea3c:563d778894e2a41d4a0d9a7fa9d90ba9:2afb0b8dfa048ea2bbd02e313d2c7964$</string>
            <key>textureFileName</key>
            <string>bullet.png</string>
        </dict>
    </dict>
</plist>
