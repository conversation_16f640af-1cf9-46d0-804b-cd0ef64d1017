[{"__type__": "cc.Prefab", "_name": "SkilItem", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "SkilItem", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 8}, {"__id__": 14}, {"__id__": 20}], "_active": true, "_components": [{"__id__": 26}, {"__id__": 28}], "_prefab": {"__id__": 30}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 5}], "_prefab": {"__id__": 7}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 4}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2bUkO/fnVJZZ/YGLUTSXs2"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 6}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f7nVJjNnxLp68S/3uKP8OJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9aVO64pPJNl6aCsK8NbBbk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 9}, {"__id__": 11}], "_prefab": {"__id__": 13}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": {"__id__": 10}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "317bRBbSVICayNLGgRo+Cy"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": {"__id__": 12}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9b0e48e7-4f33-400a-b748-4c5ea81925c8@1486f", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0f4FebahtCOoezJvPYUQIJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "75fL3OeGRIUJ6iUFdN5n/+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Mask", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 15}, {"__id__": 17}], "_prefab": {"__id__": 19}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": {"__id__": 16}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "82xgEPH61NaZX8G91N/kYP"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": {"__id__": 18}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a82PMoTahNQoycZJ8FtYPF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "88z29upAVFTJeqnqf+G3X2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 21}, {"__id__": 23}], "_prefab": {"__id__": 25}, "_lpos": {"__type__": "cc.Vec3", "x": 2.517, "y": -25.168, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 20}, "_enabled": true, "__prefab": {"__id__": 22}, "_contentSize": {"__type__": "cc.Size", "width": 4, "height": 41.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7ax1uUy/tN87g/CWD6/R9J"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 20}, "_enabled": true, "__prefab": {"__id__": 24}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f8pefhL7tJ7aYY0ojtHZKM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f1X/nXsbBGJrS7K6efMOcf", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 27}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3cIqcxmCZIEr6+lKWf++Fm"}, {"__type__": "b55d8zE3jtIN57gY/pAUdXH", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 29}, "skillData": "", "frame20": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@ac77e", "__expectedType__": "cc.SpriteFrame"}, "frame26": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@fc261", "__expectedType__": "cc.SpriteFrame"}, "frame31": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@f5077", "__expectedType__": "cc.SpriteFrame"}, "frame30": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@f350a", "__expectedType__": "cc.SpriteFrame"}, "frame25": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@2e3d0", "__expectedType__": "cc.SpriteFrame"}, "frame42": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@386e8", "__expectedType__": "cc.SpriteFrame"}, "frame45": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@0917f", "__expectedType__": "cc.SpriteFrame"}, "frame36": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@b01e2", "__expectedType__": "cc.SpriteFrame"}, "frame28": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@cbc69", "__expectedType__": "cc.SpriteFrame"}, "frame34": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@cfb42", "__expectedType__": "cc.SpriteFrame"}, "frame48": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@a223d", "__expectedType__": "cc.SpriteFrame"}, "frame50": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@05497", "__expectedType__": "cc.SpriteFrame"}, "frame46": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@c4c1a", "__expectedType__": "cc.SpriteFrame"}, "frame44": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@c4f99", "__expectedType__": "cc.SpriteFrame"}, "frame24": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@7a5c6", "__expectedType__": "cc.SpriteFrame"}, "frame27": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@a3e24", "__expectedType__": "cc.SpriteFrame"}, "frame35": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@61c6f", "__expectedType__": "cc.SpriteFrame"}, "frame37": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@e575f", "__expectedType__": "cc.SpriteFrame"}, "frame38": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@3690b", "__expectedType__": "cc.SpriteFrame"}, "frame39": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@01e41", "__expectedType__": "cc.SpriteFrame"}, "frame41": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@330b3", "__expectedType__": "cc.SpriteFrame"}, "userFrame": {"__uuid__": "1c504167-573d-454a-8c9f-002cff4c69fd@9d186", "__expectedType__": "cc.SpriteFrame"}, "pickFrame": {"__uuid__": "9bbc6298-a71a-4fd3-add7-e55197b72c91@f9941", "__expectedType__": "cc.SpriteFrame"}, "BoomFrame": {"__uuid__": "9b0e48e7-4f33-400a-b748-4c5ea81925c8@8e16b", "__expectedType__": "cc.SpriteFrame"}, "heartFrame": {"__uuid__": "91fa3bf8-8d3d-428e-b673-53cc7a0006ab@babd8", "__expectedType__": "cc.SpriteFrame"}, "lightFrame": {"__uuid__": "4fd5d3dd-d83b-4fdc-ae45-328700ed2269@fd7a9", "__expectedType__": "cc.SpriteFrame"}, "backpackFrame": {"__uuid__": "8bee7713-5040-4609-b9bc-72fbcb748882@d547a", "__expectedType__": "cc.SpriteFrame"}, "ladderFrame": {"__uuid__": "91fa3bf8-8d3d-428e-b673-53cc7a0006ab@37b53", "__expectedType__": "cc.SpriteFrame"}, "flyBoomFrame": {"__uuid__": "0ce747ea-f57b-467a-9600-3a2ecf3b4ba2@ccdfd", "__expectedType__": "cc.SpriteFrame"}, "downBoomFrame": {"__uuid__": "7c8b3032-63ae-4385-b5a7-a7bca7809a95@83738", "__expectedType__": "cc.SpriteFrame"}, "nodeName": "", "parentId": "", "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b7gNntLa5O3acF/O19B8hO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5d959l1VxOQIjp+CRAZz2j", "instance": null, "targetOverrides": null}]