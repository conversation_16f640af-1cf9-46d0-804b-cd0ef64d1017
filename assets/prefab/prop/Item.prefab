[{"__type__": "cc.Prefab", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [{"__id__": 20}, {"__id__": 22}, {"__id__": 24}], "_prefab": {"__id__": 26}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Layout", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 9}], "_active": true, "_components": [{"__id__": 15}, {"__id__": 17}], "_prefab": {"__id__": 19}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}, {"__id__": 6}], "_prefab": {"__id__": 8}, "_lpos": {"__type__": "cc.Vec3", "x": -31.1943359375, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 5}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cdCxQa64RAHbNyu5mQVsLv"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 7}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@f350a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "42sjT5hk1HsLY/QEAQMaRW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "447GaxCmVAtIWmJSCYTCmD", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 10}, {"__id__": 12}], "_prefab": {"__id__": 14}, "_lpos": {"__type__": "cc.Vec3", "x": 20, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 9}, "_enabled": true, "__prefab": {"__id__": 11}, "_contentSize": {"__type__": "cc.Size", "width": 62.*********, "height": 44.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "74VEAfDzpLE7qN1koiqWmc"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 9}, "_enabled": true, "__prefab": {"__id__": 13}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "1.5k", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 32, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "39NpKABBlGo4s/xsimwz1m"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c2JYKZv3FB0KnD7LJKTDMg", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 16}, "_contentSize": {"__type__": "cc.Size", "width": 112.*********, "height": 56}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6a3BJKnxdHfJrf6BxZsyQG"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 18}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 5, "_paddingRight": 5, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "15hmBXhpRCXZY9Xwv6GdIV"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "89XFJLK/VKopwCDy+88Jw9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 21}, "_contentSize": {"__type__": "cc.Size", "width": 130, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "61nYeiXQ5JToxfUSvIQ3ub"}, {"__type__": "8a31asXnKNDsp0EXuDhajfN", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 23}, "frame20": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@ac77e", "__expectedType__": "cc.SpriteFrame"}, "frame26": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@fc261", "__expectedType__": "cc.SpriteFrame"}, "frame31": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@f5077", "__expectedType__": "cc.SpriteFrame"}, "frame30": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@f350a", "__expectedType__": "cc.SpriteFrame"}, "frame25": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@2e3d0", "__expectedType__": "cc.SpriteFrame"}, "frame42": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@386e8", "__expectedType__": "cc.SpriteFrame"}, "frame45": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@0917f", "__expectedType__": "cc.SpriteFrame"}, "frame36": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@b01e2", "__expectedType__": "cc.SpriteFrame"}, "frame28": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@cbc69", "__expectedType__": "cc.SpriteFrame"}, "frame34": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@cfb42", "__expectedType__": "cc.SpriteFrame"}, "frame48": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@a223d", "__expectedType__": "cc.SpriteFrame"}, "frame50": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@05497", "__expectedType__": "cc.SpriteFrame"}, "frame46": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@c4c1a", "__expectedType__": "cc.SpriteFrame"}, "frame44": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@c4f99", "__expectedType__": "cc.SpriteFrame"}, "frame24": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@7a5c6", "__expectedType__": "cc.SpriteFrame"}, "frame27": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@a3e24", "__expectedType__": "cc.SpriteFrame"}, "frame35": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@61c6f", "__expectedType__": "cc.SpriteFrame"}, "frame37": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@e575f", "__expectedType__": "cc.SpriteFrame"}, "frame38": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@3690b", "__expectedType__": "cc.SpriteFrame"}, "frame39": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@01e41", "__expectedType__": "cc.SpriteFrame"}, "frame41": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@330b3", "__expectedType__": "cc.SpriteFrame"}, "coinFrame": {"__uuid__": "91fa3bf8-8d3d-428e-b673-53cc7a0006ab@9a42e", "__expectedType__": "cc.SpriteFrame"}, "price": "", "oreName": "", "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "03Pz1fn7tGOp3d8/Fq/DIL"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 25}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aa6l4UsrRDcrm24nAqq+J+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bewejGRdxHa7QI9RqEMYST", "instance": null, "targetOverrides": null}]