[{"__type__": "cc.SceneAsset", "_name": "hiddenRoom", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_name": "hiddenRoom", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [], "_prefab": {"__id__": 355}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "autoReleaseAssets": false, "_globals": {"__id__": 356}, "_id": "e4c55267-f76b-48d0-8e02-efd465efcaf7"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 11}, {"__id__": 13}, {"__id__": 15}, {"__id__": 197}, {"__id__": 200}, {"__id__": 208}, {"__id__": 218}, {"__id__": 220}, {"__id__": 19}], "_active": true, "_components": [{"__id__": 352}, {"__id__": 353}, {"__id__": 354}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 360, "y": 640, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "beI88Z2HpFELqR4T5EMHpg"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 2}, "_prefab": {"__id__": 4}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 3}, "asset": {"__uuid__": "f037cad0-306f-45ef-9196-d43452072272", "__expectedType__": "cc.Prefab"}, "fileId": "fdxwQqRvtNvYaWaUumVqD2", "instance": {"__id__": 5}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "c3geHTqb9N15cYdLiLVnOX", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 6}, {"__id__": 8}, {"__id__": 9}, {"__id__": 10}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 7}, "propertyPath": ["_name"], "value": "CommonAssets"}, {"__type__": "cc.TargetInfo", "localID": ["fdxwQqRvtNvYaWaUumVqD2"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 7}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 7}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 7}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_name": "MainCamera", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 12}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9bp4fNGPdLV4WXECAc/kfU"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 2, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 640, "_near": 0, "_far": 1000, "_color": {"__type__": "cc.Color", "r": 31, "g": 30, "b": 30, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 7, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 1073741824, "_targetTexture": null, "_postProcess": null, "_usePostProcess": false, "_cameraType": -1, "_trackingType": 0, "_id": "8aON0FDJVKX7tZ/7fo9m7o"}, {"__type__": "cc.Node", "_name": "UICamera", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 14}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "47nH0iQlxDELTjCw8MkrOj"}, {"__type__": "cc.Camera", "_name": "Camera<CameraComponent>", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 3, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 640, "_near": 0, "_far": 1000, "_color": {"__type__": "cc.Color", "r": 15, "g": 14, "b": 14, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 6, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 33554432, "_targetTexture": null, "_postProcess": null, "_usePostProcess": false, "_cameraType": -1, "_trackingType": 0, "_id": "d0Db4jkZVKC6xaNInueTLm"}, {"__type__": "cc.Node", "_name": "GameManager", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 16}, {"__id__": 17}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "63yV2LzqlA96vanrbhxjCh"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 15}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "efNQFTLrFLGYuygeokXs+8"}, {"__type__": "fd7d1CzSZFJrZ09qgecGvJy", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 15}, "_enabled": true, "__prefab": null, "mainCamera": {"__id__": 12}, "uiCamera": {"__id__": 14}, "gameoverMaskFab": {"__uuid__": "4675d05d-9649-416a-9e06-ac4caba809a2", "__expectedType__": "cc.Prefab"}, "backpackItem": {"__uuid__": "ebba3783-235e-4cbb-9b86-508d1e83fa9b", "__expectedType__": "cc.Prefab"}, "backpack": {"__id__": 18}, "backpackTwo": {"__id__": 144}, "coinNode": {"__id__": 34}, "coinItem": {"__uuid__": "ed9b679a-1046-46b2-9445-98216fc80343", "__expectedType__": "cc.Prefab"}, "backpackS": {"__uuid__": "c9280fa7-7911-4fcd-bab1-fba366238dc6", "__expectedType__": "cc.Prefab"}, "frame19": {"__uuid__": "91fa3bf8-8d3d-428e-b673-53cc7a0006ab@e4d93", "__expectedType__": "cc.SpriteFrame"}, "frame20": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@ac77e", "__expectedType__": "cc.SpriteFrame"}, "frame26": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@fc261", "__expectedType__": "cc.SpriteFrame"}, "frame31": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@f5077", "__expectedType__": "cc.SpriteFrame"}, "frame30": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@f350a", "__expectedType__": "cc.SpriteFrame"}, "frame25": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@2e3d0", "__expectedType__": "cc.SpriteFrame"}, "frame42": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@386e8", "__expectedType__": "cc.SpriteFrame"}, "frame45": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@0917f", "__expectedType__": "cc.SpriteFrame"}, "frame36": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@b01e2", "__expectedType__": "cc.SpriteFrame"}, "frame28": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@cbc69", "__expectedType__": "cc.SpriteFrame"}, "frame34": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@cfb42", "__expectedType__": "cc.SpriteFrame"}, "frame48": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@a223d", "__expectedType__": "cc.SpriteFrame"}, "frame50": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@05497", "__expectedType__": "cc.SpriteFrame"}, "frame46": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@c4c1a", "__expectedType__": "cc.SpriteFrame"}, "frame44": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@c4f99", "__expectedType__": "cc.SpriteFrame"}, "frame24": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@7a5c6", "__expectedType__": "cc.SpriteFrame"}, "frame27": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@a3e24", "__expectedType__": "cc.SpriteFrame"}, "frame35": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@61c6f", "__expectedType__": "cc.SpriteFrame"}, "frame37": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@e575f", "__expectedType__": "cc.SpriteFrame"}, "frame38": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@3690b", "__expectedType__": "cc.SpriteFrame"}, "frame39": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@01e41", "__expectedType__": "cc.SpriteFrame"}, "frame41": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@330b3", "__expectedType__": "cc.SpriteFrame"}, "itemFloatUpPrefab": {"__uuid__": "9eb4ef7a-8cd5-47a9-b798-967e7033e635", "__expectedType__": "cc.Prefab"}, "_id": "f1x9tmpBZFrrCS77hSWnIo"}, {"__type__": "cc.Node", "_name": "Backpack", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 19}, "_children": [{"__id__": 75}, {"__id__": 144}], "_active": false, "_components": [{"__id__": 195}, {"__id__": 196}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 113.38, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8aO/utWpBJ4pnCU05YAuYJ"}, {"__type__": "cc.Node", "_name": "UI", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 20}, {"__id__": 18}, {"__id__": 34}, {"__id__": 44}, {"__id__": 54}, {"__id__": 63}], "_active": true, "_components": [{"__id__": 72}, {"__id__": 73}, {"__id__": 74}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d5FGX3FNFIDYYD/dVdKjqx"}, {"__type__": "cc.Node", "_name": "GamePad", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 19}, "_children": [{"__id__": 21}, {"__id__": 24}, {"__id__": 29}], "_active": true, "_components": [{"__id__": 32}, {"__id__": 33}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 54.492, "y": -356.923, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ddGhlmRS1P9ar8b0mPu7qG"}, {"__type__": "cc.Node", "_name": "Bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [], "_active": true, "_components": [{"__id__": 22}, {"__id__": 23}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9fomUIizRB3aqiouxMGfuI"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 21}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 280, "height": 280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "062iBOnyZBvLoiESwN2SRl"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 21}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "87e01919-9420-4626-9f66-4bb30feb9690@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "b4dSUOXZpLlqJjnn3IXgsk"}, {"__type__": "cc.Node", "_name": "Direct", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [{"__id__": 25}], "_active": true, "_components": [{"__id__": 28}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "78pCliuRpJTZWnbGlz8JA0"}, {"__type__": "cc.Node", "_name": "Bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 24}, "_children": [], "_active": true, "_components": [{"__id__": 26}, {"__id__": 27}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 6, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f5JOEO6NVB+4vfzZLZCqMz"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 115, "height": 171}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": "cd1tHcqhVLo4F0JGXGEc6U"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0e8b8531-9873-44a4-912b-95164dfed23c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "dfaeLypYJJLo07eilqjfFp"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 24}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 121, "height": 171}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": "48JB8xfPRNj500Yf3eod4x"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [], "_active": true, "_components": [{"__id__": 30}, {"__id__": 31}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 2.596, "y": -0.98, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "16SlM3eG9EE41gqPIYfTph"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 29}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 59, "height": 59}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d553f7IrVCkINVpqU8CKqz"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 29}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bc961abe-8492-4eda-ab8c-39cefb85a079@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "9c/IpnDB9OFq4+uyvgIWDl"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 20}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 280, "height": 280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1ffaJuM/dHlJpb8Jo7kW/k"}, {"__type__": "af378D6cTpIaIaVx6xMiJOO", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 20}, "_enabled": true, "__prefab": null, "from": "hiddenRoom", "_id": "29hBOcg2RIeprIk+zBMmci"}, {"__type__": "cc.Node", "_name": "Coin", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 19}, "_children": [{"__id__": 35}, {"__id__": 38}], "_active": true, "_components": [{"__id__": 41}, {"__id__": 42}, {"__id__": 43}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -298, "y": 438.143, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "21DHwzAEdDeo6Jq2dviam9"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 34}, "_children": [], "_active": true, "_components": [{"__id__": 36}, {"__id__": 37}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "82iDkSc1hJwq5GyKVlT5Wd"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 35}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "cf/mdUJcdDAJMsY5kfQV7r"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 35}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5a9041ee-b7b1-493e-8d70-91e9333f692f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "bccsesiu9DTZwVEZI8FgmE"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 34}, "_children": [], "_active": true, "_components": [{"__id__": 39}, {"__id__": 40}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 30, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "1aVz+RAH5Mjb7sz3RNfuP3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 38}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 23.889999389648438, "height": 41.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": "365H870L1BUrF6lO6N3zaJ"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 38}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "0", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a02df6db-4f52-4023-8bfb-6429098d02cf", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "5cYMF8QOpKP6KK7o52/B8q"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 34}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c2+c4Vn9FOTaywmL9xrzII"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 34}, "_enabled": true, "__prefab": null, "_alignFlags": 9, "_target": null, "_left": 12, "_right": 0, "_top": 171.85700000000003, "_bottom": 0, "_horizontalCenter": -300, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "ffKnl4HDVDPJO8G4e+hk78"}, {"__type__": "b0a386NHWhNXaANc0EX/Etg", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 34}, "_enabled": true, "__prefab": null, "coinItem": {"__uuid__": "e6ddf297-a19f-4fb5-959d-37f36ad1751a", "__expectedType__": "cc.Prefab"}, "endCount": 0, "_id": "4arEKVwBlMJrh/1x9PlF+i"}, {"__type__": "cc.Node", "_name": "Dirt", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 19}, "_children": [{"__id__": 45}, {"__id__": 48}], "_active": true, "_components": [{"__id__": 51}, {"__id__": 52}, {"__id__": 53}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -298, "y": 258.143, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "cd/sl5wohABrku9dZb1U0E"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 44}, "_children": [], "_active": true, "_components": [{"__id__": 46}, {"__id__": 47}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8f7FL1qh5Bvbub+Pd/4VgC"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 45}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 44, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "91/QlS8cJC3aLsJQqRJJWe"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 45}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "91fa3bf8-8d3d-428e-b673-53cc7a0006ab@e8b5d", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "0alhsakP1CPIfdUUYs7FUQ"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 44}, "_children": [], "_active": true, "_components": [{"__id__": 49}, {"__id__": 50}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 30, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "30jTjiPXFKHoImJI1GkcXk"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 48}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 23.889999389648438, "height": 41.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": "41o92CKFpObL/3CGU/efS+"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 48}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "0", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a02df6db-4f52-4023-8bfb-6429098d02cf", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "e8ERDZllhFC5NllGtKyjEt"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 44}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b5lGOSL+lIAoGKskgF5Nfx"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 44}, "_enabled": true, "__prefab": null, "_alignFlags": 9, "_target": null, "_left": 12, "_right": 0, "_top": 351.857, "_bottom": 0, "_horizontalCenter": -300, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "f4WJfXNLNFA5nIfnpL1wye"}, {"__type__": "b0a386NHWhNXaANc0EX/Etg", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 44}, "_enabled": true, "__prefab": null, "coinItem": {"__uuid__": "e6ddf297-a19f-4fb5-959d-37f36ad1751a", "__expectedType__": "cc.Prefab"}, "endCount": 0, "_id": "deaPJO5oBCWK0GSGmITg35"}, {"__type__": "cc.Node", "_name": "Diamond", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 19}, "_children": [{"__id__": 55}, {"__id__": 58}], "_active": true, "_components": [{"__id__": 61}, {"__id__": 62}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -298, "y": 378.143, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ecK0a1p/VG2aaWv5ch8OwK"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 54}, "_children": [], "_active": true, "_components": [{"__id__": 56}, {"__id__": 57}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a5DpsNX5dP1pjP0zjMivae"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 55}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 44, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "dflrVAQTpIOIhXEocAReHL"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 55}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d28b8e35-5d48-4497-8a60-9a82813dad72@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "0cAlSfyhxI+7zi/V+ngLFw"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 54}, "_children": [], "_active": true, "_components": [{"__id__": 59}, {"__id__": 60}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 30, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "3eHVxXtClN9bbmjOIy4c9p"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 58}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 23.889999389648438, "height": 41.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": "947FO3TbROH5zPwltth6z2"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 58}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "0", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a02df6db-4f52-4023-8bfb-6429098d02cf", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "7892IWaPtIMKX30pgPpcY1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 54}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "900OztdZ5DD6LmkxyB4ZGB"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 54}, "_enabled": true, "__prefab": null, "_alignFlags": 9, "_target": null, "_left": 12, "_right": 0, "_top": 231.85700000000003, "_bottom": 0, "_horizontalCenter": -300, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "dea8o/+bVJXLydxS4MPhAt"}, {"__type__": "cc.Node", "_name": "Keys", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 19}, "_children": [{"__id__": 64}, {"__id__": 67}], "_active": true, "_components": [{"__id__": 70}, {"__id__": 71}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -298, "y": 318.143, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8f4uavGJ9NooZxj+vfZIip"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 63}, "_children": [], "_active": true, "_components": [{"__id__": 65}, {"__id__": 66}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a6isb0w6pFMZdE1+aOEPvU"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 64}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 44, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "4aUGmqD7dNVrQI5G4UJ9B3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 64}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "974469ff-18f5-4693-bdc1-5c7ebbdedaa2@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "60HvJvj+xCAqyJRk5SvMjk"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 63}, "_children": [], "_active": true, "_components": [{"__id__": 68}, {"__id__": 69}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 30, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "33SmDqIphCEacguYN8dt0d"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 67}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 23.889999389648438, "height": 41.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": "d0oq8XlMlDSpVukp0N4yWz"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 67}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "0", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a02df6db-4f52-4023-8bfb-6429098d02cf", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "f6mozflERGQ7X3bIyHCHIO"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 63}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "50Xm5MyhtCXJB8R80RF7r8"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 63}, "_enabled": true, "__prefab": null, "_alignFlags": 9, "_target": null, "_left": 12, "_right": 0, "_top": 291.857, "_bottom": 0, "_horizontalCenter": -300, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "173NgZSflJALEUqfQieVEX"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "10xq/jLsFEuLeoFoMgz29b"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": "ectY8DFyRCSq4OdSEcque1"}, {"__type__": "0140fWg/9FJn4PffzAXF3Pf", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": null, "_id": "31jvx/MlBBI4AajHZQ+5TJ"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 18}, "_children": [{"__id__": 76}, {"__id__": 94}, {"__id__": 110}, {"__id__": 126}], "_active": true, "_components": [{"__id__": 142}, {"__id__": 143}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 55, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "65r3rEB7hDdZ46qiau9L0I"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 75}, "_prefab": {"__id__": 77}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 76}, "asset": {"__uuid__": "ebba3783-235e-4cbb-9b86-508d1e83fa9b", "__expectedType__": "cc.Prefab"}, "fileId": "70nnnJJtdDZ6rdoQwqzAfq", "instance": {"__id__": 78}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "8ch+bCeXZM14kV03VXPleZ", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 79}, {"__id__": 81}, {"__id__": 82}, {"__id__": 83}, {"__id__": 84}, {"__id__": 86}, {"__id__": 87}, {"__id__": 89}, {"__id__": 90}, {"__id__": 92}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 80}, "propertyPath": ["_name"], "value": "BackpackItem"}, {"__type__": "cc.TargetInfo", "localID": ["70nnnJJtdDZ6rdoQwqzAfq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 80}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 80}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 80}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 85}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 72.3642578125, "height": 41.8}}, {"__type__": "cc.TargetInfo", "localID": ["94d4tlUX9L6Joo6P1wO3jv"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 80}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 88}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 80, "height": 80}}, {"__type__": "cc.TargetInfo", "localID": ["62cZDZRcNAJrqHanui5lAr"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 80}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 91}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["2fE7vLHidCDpCpem7fCzxL"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 93}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["67L+86sPNGnJjBtwiWqKbV"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 75}, "_prefab": {"__id__": 95}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 94}, "asset": {"__uuid__": "ebba3783-235e-4cbb-9b86-508d1e83fa9b", "__expectedType__": "cc.Prefab"}, "fileId": "70nnnJJtdDZ6rdoQwqzAfq", "instance": {"__id__": 96}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "3cWjtCm4BIG6lKeJpc7GIn", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 97}, {"__id__": 99}, {"__id__": 100}, {"__id__": 101}, {"__id__": 102}, {"__id__": 103}, {"__id__": 105}, {"__id__": 106}, {"__id__": 108}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 98}, "propertyPath": ["_name"], "value": "BackpackItem"}, {"__type__": "cc.TargetInfo", "localID": ["70nnnJJtdDZ6rdoQwqzAfq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 98}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -90, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 98}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 98}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 98}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 104}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 80, "height": 80}}, {"__type__": "cc.TargetInfo", "localID": ["62cZDZRcNAJrqHanui5lAr"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 98}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 107}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["2fE7vLHidCDpCpem7fCzxL"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 109}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["67L+86sPNGnJjBtwiWqKbV"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 75}, "_prefab": {"__id__": 111}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 110}, "asset": {"__uuid__": "ebba3783-235e-4cbb-9b86-508d1e83fa9b", "__expectedType__": "cc.Prefab"}, "fileId": "70nnnJJtdDZ6rdoQwqzAfq", "instance": {"__id__": 112}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "94ZbtF/jpAHY5Xz59eOdUY", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 113}, {"__id__": 115}, {"__id__": 116}, {"__id__": 117}, {"__id__": 118}, {"__id__": 119}, {"__id__": 121}, {"__id__": 122}, {"__id__": 124}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 114}, "propertyPath": ["_name"], "value": "BackpackItem"}, {"__type__": "cc.TargetInfo", "localID": ["70nnnJJtdDZ6rdoQwqzAfq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 114}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -45, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 114}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 114}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 114}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 120}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 80, "height": 80}}, {"__type__": "cc.TargetInfo", "localID": ["62cZDZRcNAJrqHanui5lAr"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 114}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 123}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["2fE7vLHidCDpCpem7fCzxL"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 125}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["67L+86sPNGnJjBtwiWqKbV"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 75}, "_prefab": {"__id__": 127}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 126}, "asset": {"__uuid__": "ebba3783-235e-4cbb-9b86-508d1e83fa9b", "__expectedType__": "cc.Prefab"}, "fileId": "70nnnJJtdDZ6rdoQwqzAfq", "instance": {"__id__": 128}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "256xaAsnlGI5zuGLM9bbSx", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 129}, {"__id__": 131}, {"__id__": 132}, {"__id__": 133}, {"__id__": 134}, {"__id__": 135}, {"__id__": 137}, {"__id__": 138}, {"__id__": 140}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 130}, "propertyPath": ["_name"], "value": "BackpackItem"}, {"__type__": "cc.TargetInfo", "localID": ["70nnnJJtdDZ6rdoQwqzAfq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 130}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 130}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 130}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 130}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 136}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 80, "height": 80}}, {"__type__": "cc.TargetInfo", "localID": ["62cZDZRcNAJrqHanui5lAr"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 130}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 139}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["2fE7vLHidCDpCpem7fCzxL"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 141}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["67L+86sPNGnJjBtwiWqKbV"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 75}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 10, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1dtQw4dpFMkILLAGjN/JTr"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 75}, "_enabled": true, "__prefab": null, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 10, "_paddingRight": 10, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 10, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": "2a9mVvfG9Ib6Nkfqu4dLsI"}, {"__type__": "cc.Node", "_name": "Item-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 18}, "_children": [{"__id__": 145}, {"__id__": 161}, {"__id__": 177}], "_active": true, "_components": [{"__id__": 193}, {"__id__": 194}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -55, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2f5zD0pelBarULywRYhS/V"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 144}, "_prefab": {"__id__": 146}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 145}, "asset": {"__uuid__": "ebba3783-235e-4cbb-9b86-508d1e83fa9b", "__expectedType__": "cc.Prefab"}, "fileId": "70nnnJJtdDZ6rdoQwqzAfq", "instance": {"__id__": 147}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "c925sWS4xMXL0lX1jIpbTK", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 148}, {"__id__": 150}, {"__id__": 151}, {"__id__": 152}, {"__id__": 153}, {"__id__": 154}, {"__id__": 156}, {"__id__": 157}, {"__id__": 159}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 149}, "propertyPath": ["_name"], "value": "BackpackItem"}, {"__type__": "cc.TargetInfo", "localID": ["70nnnJJtdDZ6rdoQwqzAfq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 149}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -90, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 149}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 149}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 149}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 155}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 80, "height": 80}}, {"__type__": "cc.TargetInfo", "localID": ["62cZDZRcNAJrqHanui5lAr"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 149}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 158}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["2fE7vLHidCDpCpem7fCzxL"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 160}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["67L+86sPNGnJjBtwiWqKbV"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 144}, "_prefab": {"__id__": 162}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 161}, "asset": {"__uuid__": "ebba3783-235e-4cbb-9b86-508d1e83fa9b", "__expectedType__": "cc.Prefab"}, "fileId": "70nnnJJtdDZ6rdoQwqzAfq", "instance": {"__id__": 163}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "7cVBAwhmZPHJQDcXauCiZ8", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 164}, {"__id__": 166}, {"__id__": 167}, {"__id__": 168}, {"__id__": 169}, {"__id__": 170}, {"__id__": 172}, {"__id__": 173}, {"__id__": 175}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 165}, "propertyPath": ["_name"], "value": "BackpackItem"}, {"__type__": "cc.TargetInfo", "localID": ["70nnnJJtdDZ6rdoQwqzAfq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 165}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -45, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 165}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 165}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 165}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 171}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 80, "height": 80}}, {"__type__": "cc.TargetInfo", "localID": ["62cZDZRcNAJrqHanui5lAr"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 165}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 174}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["2fE7vLHidCDpCpem7fCzxL"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 176}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["67L+86sPNGnJjBtwiWqKbV"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 144}, "_prefab": {"__id__": 178}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 177}, "asset": {"__uuid__": "ebba3783-235e-4cbb-9b86-508d1e83fa9b", "__expectedType__": "cc.Prefab"}, "fileId": "70nnnJJtdDZ6rdoQwqzAfq", "instance": {"__id__": 179}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "bf3OYPpxJEoYntOP7xU0CI", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 180}, {"__id__": 182}, {"__id__": 183}, {"__id__": 184}, {"__id__": 185}, {"__id__": 186}, {"__id__": 188}, {"__id__": 189}, {"__id__": 191}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 181}, "propertyPath": ["_name"], "value": "BackpackItem"}, {"__type__": "cc.TargetInfo", "localID": ["70nnnJJtdDZ6rdoQwqzAfq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 181}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 181}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 181}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 181}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 187}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 80, "height": 80}}, {"__type__": "cc.TargetInfo", "localID": ["62cZDZRcNAJrqHanui5lAr"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 181}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 190}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["2fE7vLHidCDpCpem7fCzxL"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 192}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["67L+86sPNGnJjBtwiWqKbV"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 144}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 10, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "20nr4vp5FHWrQ9s4/I18N/"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 144}, "_enabled": true, "__prefab": null, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 10, "_paddingRight": 10, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 10, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": "8a+wb+aHtHZ6cLdKQRyzAQ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 560, "height": 210}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "fezMX5DX9BmIbgwZnCz00l"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": null, "_alignFlags": 17, "_target": null, "_left": 0, "_right": 0, "_top": 421.62, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 440, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "9cliV43WpPMrh4usylOkHo"}, {"__type__": "cc.Node", "_name": "Bg2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 198}, {"__id__": 199}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f9vG6jEb5IVqzRJKg/0eVY"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 197}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1334, "height": 750}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "4bGmHPs6hOnJ2OOFIsi+P1"}, {"__type__": "d59daH1hWpIZoj8SelaMI/s", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 197}, "_enabled": true, "__prefab": null, "layer0": {"__uuid__": "46ccd5b3-9c02-429d-b507-48891d63084d@f9941", "__expectedType__": "cc.SpriteFrame"}, "layer1": {"__uuid__": "5bc02f4e-3d33-4f3b-8d69-709c1c357728@f9941", "__expectedType__": "cc.SpriteFrame"}, "_id": "6dIK0MWo5PObsCV29bB4S0"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 2}, "_prefab": {"__id__": 201}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 200}, "asset": {"__uuid__": "c69fd81c-065d-455f-8bdc-c3823ea365c4", "__expectedType__": "cc.Prefab"}, "fileId": "79KPIXoZxJe6wS2JD0uV7O", "instance": {"__id__": 202}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "82Lf+8LH9GKZZkPScm15TD", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 203}, {"__id__": 205}, {"__id__": 206}, {"__id__": 207}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 204}, "propertyPath": ["_name"], "value": "Exit"}, {"__type__": "cc.TargetInfo", "localID": ["79KPIXoZxJe6wS2JD0uV7O"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 204}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 204}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 204}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_name": "TiledMap", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 209}, {"__id__": 212}], "_active": true, "_components": [{"__id__": 215}, {"__id__": 216}, {"__id__": 217}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 103.32200000000012, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1.2, "y": 1.2, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "3bfSv6H+pI0JL/+ED0jicO"}, {"__type__": "cc.Node", "_name": "door_samples", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 208}, "_children": [], "_active": false, "_components": [{"__id__": 210}, {"__id__": 211}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c3itK2IoFPrLzTdiuoG5fN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 209}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 704, "height": 16448}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_id": "753bwgeQpNA6jotIOtnM/Z"}, {"__type__": "cc.T<PERSON><PERSON>ayer", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 209}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_id": "96L3Wm665ESIjZmuchnNsd"}, {"__type__": "cc.Node", "_name": "objects", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 208}, "_children": [], "_active": false, "_components": [{"__id__": 213}, {"__id__": 214}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 352, "y": -8224, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "43HaaSn4JEqa1RpTGy9nP7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 212}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 704, "height": 16448}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "50JeVOUoxIbY2XcBeqyK/i"}, {"__type__": "cc.TiledObjectGroup", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 212}, "_enabled": true, "__prefab": null, "_id": "58PQw6E9dDV5BqZ83DJ/vc"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 208}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 704, "height": 9728}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_id": "1cTgQTjzlNn7M9zTFIu/m+"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 208}, "_enabled": true, "__prefab": null, "_alignFlags": 17, "_target": null, "_left": 0, "_right": 0, "_top": 0.41927968749999994, "_bottom": 0, "_horizontalCenter": 422.4, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": false, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "847ZxWeXBOiJp5wYhSFnx6"}, {"__type__": "b0483XUuhpJwLac1lFykffg", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 208}, "_enabled": true, "__prefab": null, "playerFab": {"__uuid__": "67f46178-a21c-4553-926e-510ee06502f9", "__expectedType__": "cc.Prefab"}, "scarFab": {"__uuid__": "2558625c-17c1-4e7d-8b75-29c76460c38a", "__expectedType__": "cc.Prefab"}, "nextMinePortalFrame": {"__uuid__": "fca7783c-1757-40b3-93b6-e293771cc2d4@f9941", "__expectedType__": "cc.SpriteFrame"}, "playerBuffPrefab": {"__uuid__": "86bac04d-f061-479a-8767-c23afdd5bea4", "__expectedType__": "cc.Prefab"}, "mine01Fab": {"__uuid__": "927ad900-899e-442c-9122-8d77bd9a4cf4", "__expectedType__": "cc.Prefab"}, "trap01Fab": {"__uuid__": "db3429ef-d9b5-4b2b-bec4-da42d2b8bdee", "__expectedType__": "cc.Prefab"}, "bombFab": {"__uuid__": "392c28c1-58a1-4773-b350-cc537612c9c7", "__expectedType__": "cc.Prefab"}, "bombFlyingFab": {"__uuid__": "5dd79302-6470-4daa-8e14-72518f9ae113", "__expectedType__": "cc.Prefab"}, "bombGrenadeFab": {"__uuid__": "f9f19227-c013-4d46-9050-9c248b8bcfe5", "__expectedType__": "cc.Prefab"}, "rockFallFab": {"__uuid__": "14dc2cf9-f175-42bb-b214-116d7553c253", "__expectedType__": "cc.Prefab"}, "rockFall2Fab": {"__uuid__": "892f193c-d466-4ee2-8cb9-e8bd5b77503e", "__expectedType__": "cc.Prefab"}, "smokeFab": {"__uuid__": "59028983-85c5-4c25-a782-cb300790c7ef", "__expectedType__": "cc.Prefab"}, "glowingTileFab": {"__uuid__": "e22abfca-0c59-4bda-9b44-68d55c7436b5", "__expectedType__": "cc.Prefab"}, "oneCardFab": {"__uuid__": "b52e23ff-c1b5-442e-a037-b33a55f3b641", "__expectedType__": "cc.Prefab"}, "startBtnFab": {"__uuid__": "a7002569-9827-4928-aa75-6117951b4f25", "__expectedType__": "cc.Prefab"}, "dirtFrame": {"__uuid__": "91fa3bf8-8d3d-428e-b673-53cc7a0006ab@e8b5d", "__expectedType__": "cc.SpriteFrame"}, "adFrame": {"__uuid__": "9b73b7ef-d965-4a40-836d-c8756fabecc7@f9941", "__expectedType__": "cc.SpriteFrame"}, "mineHelpFab": {"__uuid__": "eccad1f9-**************-70a2190c31c1", "__expectedType__": "cc.Prefab"}, "sawFab": {"__uuid__": "e0fa2a2a-00c4-48e6-924b-bb3550a7db57", "__expectedType__": "cc.Prefab"}, "roundBladeFab": {"__uuid__": "03892478-bda0-40a6-b636-484667112d0e", "__expectedType__": "cc.Prefab"}, "twinBladeFab": {"__uuid__": "9f7620a4-ac27-4418-8403-a9031fb2e8e4", "__expectedType__": "cc.Prefab"}, "itemFloatUpPrefab": {"__uuid__": "9eb4ef7a-8cd5-47a9-b798-967e7033e635", "__expectedType__": "cc.Prefab"}, "playerHurtPrafab": {"__uuid__": "dc589047-4afa-45ae-bc95-6f3532e33163", "__expectedType__": "cc.Prefab"}, "mapBlockNodePrefab": {"__uuid__": "5c93c5e9-94ef-4c40-86cc-3ab0f7ac785b", "__expectedType__": "cc.Prefab"}, "treasureBox": {"__uuid__": "38f55e2f-ce3f-436d-aa60-06e1c45254ef", "__expectedType__": "cc.Prefab"}, "diamond": {"__uuid__": "7f61df30-bcd8-4501-8340-41c6fec2d819@f9941", "__expectedType__": "cc.SpriteFrame"}, "coin": {"__uuid__": "50fa2a1f-ee5c-414d-860c-9559214e04a6@f9941", "__expectedType__": "cc.SpriteFrame"}, "_id": "bdkjjDwbVBWrq3meDYWIwp"}, {"__type__": "cc.Node", "_name": "GameoverMask", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 219}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "96gYEA2UNG4bJZzeNZs5Qs"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 218}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "22jJ0+pFVN4pgIfjbQnB5m"}, {"__type__": "cc.Node", "_name": "Body", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 221}, {"__id__": 239}, {"__id__": 243}, {"__id__": 295}, {"__id__": 347}], "_active": false, "_components": [{"__id__": 350}, {"__id__": 351}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -2.7250000000000227, "y": -2.724999999999909, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e8Ty3xI41HmIs/ZRPutEc2"}, {"__type__": "cc.Node", "_name": "Wall", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 220}, "_children": [{"__id__": 222}, {"__id__": 225}, {"__id__": 228}, {"__id__": 231}, {"__id__": 234}], "_active": true, "_components": [{"__id__": 237}, {"__id__": 238}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 600, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f0ZauYT01HF7BWGn5jQhS+"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 221}, "_children": [], "_active": true, "_components": [{"__id__": 223}, {"__id__": 224}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 64, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 8.659560562354933e-17, "y": 8.659560562354933e-17, "z": -0.7071067811865476, "w": -0.7071067811865475}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 180, "y": 180, "z": 90}, "_id": "690iXcoBVNW4LpxGDSFMBC"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 222}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a4U1BMCTJIO51YSZRfPnD+"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 222}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "51H/PCDqFFJJ9Y534j/jCm"}, {"__type__": "cc.Node", "_name": "Sprite-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 221}, "_children": [], "_active": true, "_components": [{"__id__": 226}, {"__id__": 227}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 32, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 8.659560562354933e-17, "y": 8.659560562354933e-17, "z": -0.7071067811865476, "w": -0.7071067811865475}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 180, "y": 180, "z": 90}, "_id": "84dRxVIDtFkYjNpW0qi6C/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 225}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "71GaOS/TFOGoZn4Qax0Waj"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 225}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "f91kfmDV1K744SS+on7Q/g"}, {"__type__": "cc.Node", "_name": "Sprite-002", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 221}, "_children": [], "_active": true, "_components": [{"__id__": 229}, {"__id__": 230}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 8.659560562354933e-17, "y": 8.659560562354933e-17, "z": -0.7071067811865476, "w": -0.7071067811865475}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 180, "y": 180, "z": 90}, "_id": "15QUTGL+VOUZh2PogwMiQu"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 228}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "6dHbN0eR9JYJOSd7N35wYV"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 228}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "b0wpE7vbxH5odUIfQGk4Ym"}, {"__type__": "cc.Node", "_name": "Sprite-003", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 221}, "_children": [], "_active": true, "_components": [{"__id__": 232}, {"__id__": 233}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -32, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 8.659560562354933e-17, "y": 8.659560562354933e-17, "z": -0.7071067811865476, "w": -0.7071067811865475}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 180, "y": 180, "z": 90}, "_id": "d7zgZk/SBJ1a31J6//c57D"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 231}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b2SkVZVklCSoIlI+dG0vS0"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 231}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "8aNAUDovNJv6ULMFnnTUkq"}, {"__type__": "cc.Node", "_name": "Sprite-004", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 221}, "_children": [], "_active": true, "_components": [{"__id__": 235}, {"__id__": 236}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -64, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 8.659560562354933e-17, "y": 8.659560562354933e-17, "z": -0.7071067811865476, "w": -0.7071067811865475}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 180, "y": 180, "z": 90}, "_id": "61407ijwVNZrZO1ep6XhUb"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 234}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "12SHQ4kapKDo8KuHkLBorB"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 234}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "b2cWrWlmtGk5hLjWqKqcAh"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 221}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1f13dLLt9P35kDGpeX90Fn"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 221}, "_enabled": true, "__prefab": null, "_resizeMode": 1, "_layoutType": 2, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": "ac1znIPfNPEanJV6Z2mJsD"}, {"__type__": "cc.Node", "_name": "Door", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 220}, "_children": [], "_active": true, "_components": [{"__id__": 240}, {"__id__": 241}, {"__id__": 242}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -200, "y": -30, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.25, "y": 0.25, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4eA031fd1Fv5a7JIue7koz"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 239}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 480, "height": 305}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "6bJC54vU5EabQqCHywol2X"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 239}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "460eb54b-17d4-49d5-be38-95493a9c8e02@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "09KX0hHAdMrLip1iLuh347"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 239}, "_enabled": true, "__prefab": null, "_alignFlags": 8, "_target": null, "_left": 100, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "8eW/C9LNVFE5RnfIkyEZGI"}, {"__type__": "cc.Node", "_name": "UpRoad", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 220}, "_children": [{"__id__": 244}, {"__id__": 247}, {"__id__": 250}, {"__id__": 253}, {"__id__": 256}, {"__id__": 259}, {"__id__": 262}, {"__id__": 265}, {"__id__": 268}, {"__id__": 271}, {"__id__": 274}, {"__id__": 277}, {"__id__": 280}, {"__id__": 283}, {"__id__": 286}, {"__id__": 289}], "_active": true, "_components": [{"__id__": 292}, {"__id__": 293}, {"__id__": 294}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -360, "y": -100, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "64xtoDEfJDP6zYHvUCbY8o"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 243}, "_children": [], "_active": true, "_components": [{"__id__": 245}, {"__id__": 246}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 32, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9108sSvU1E65N3Pslkbcjw"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 244}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "73AalWrCJN27CXh1cYlHqs"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 244}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "27ImkyUyVKVLqWS+fuHDtM"}, {"__type__": "cc.Node", "_name": "Sprite-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 243}, "_children": [], "_active": true, "_components": [{"__id__": 248}, {"__id__": 249}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 96, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "beYNgBB+tPxp14LigV68W+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 247}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a2CK5C0f1NxJw7sg2HODfS"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 247}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "26K7SzGDlIV4DOH2kdUqqj"}, {"__type__": "cc.Node", "_name": "Sprite-002", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 243}, "_children": [], "_active": true, "_components": [{"__id__": 251}, {"__id__": 252}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 160, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f4uvMH6KZNa4P2WNBDco/F"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 250}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "43oGyEfMhG5rV1cfVF82qJ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 250}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "5dGVmTk4dAhp4LqNUOprmp"}, {"__type__": "cc.Node", "_name": "Sprite-003", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 243}, "_children": [], "_active": true, "_components": [{"__id__": 254}, {"__id__": 255}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 224, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "21G3L1195K66WMROrktN4Q"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 253}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "38CvIlC7FGEZtqiJcJfcP3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 253}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "28usxgu8NJGqEgICcep22R"}, {"__type__": "cc.Node", "_name": "Sprite-004", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 243}, "_children": [], "_active": true, "_components": [{"__id__": 257}, {"__id__": 258}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 288, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4ceDs1OgxDg5Pfjs3X907B"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 256}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "7biDKtRudOX4eLAdFe43co"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 256}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "9d3h6JUuRDnaFkvDRgU8Y5"}, {"__type__": "cc.Node", "_name": "Sprite-005", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 243}, "_children": [], "_active": true, "_components": [{"__id__": 260}, {"__id__": 261}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 352, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "18+NxRKVtOK6fTgIn/7+Qm"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 259}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d9pye1E5ZN2LSr5rRFOB00"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 259}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "ffZxzV/1lL1LAVY2vhiKjw"}, {"__type__": "cc.Node", "_name": "Sprite-006", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 243}, "_children": [], "_active": true, "_components": [{"__id__": 263}, {"__id__": 264}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 416, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f74rOw5P9LIrRsit7veBlt"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 262}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "34et2MzdZFxYveq247gI4q"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 262}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "b39dAvgoNOAJPzqtkSuwu0"}, {"__type__": "cc.Node", "_name": "Sprite-007", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 243}, "_children": [], "_active": true, "_components": [{"__id__": 266}, {"__id__": 267}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 480, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "40N0XMYsRGFJrTXgdiKoxh"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 265}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b8+dAxZQBH8Iepil8WWPGY"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 265}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "b5S9MI6BxMT7yOQRevM1O+"}, {"__type__": "cc.Node", "_name": "Sprite-008", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 243}, "_children": [], "_active": true, "_components": [{"__id__": 269}, {"__id__": 270}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 544, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "015BBXDs9OAokj1XKM0OyE"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 268}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c1xpx1mDhGV75IIOU4Fw1W"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 268}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "03QlwmrzBDmIjyHHb+vRU6"}, {"__type__": "cc.Node", "_name": "Sprite-009", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 243}, "_children": [], "_active": true, "_components": [{"__id__": 272}, {"__id__": 273}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 608, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "91vxSue0hFU63/9GtkBGWW"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 271}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "82FNKINGVMNancE/6V+UQX"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 271}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "f1ytFR64BLzKiIS7u3S0Wp"}, {"__type__": "cc.Node", "_name": "Sprite-010", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 243}, "_children": [], "_active": true, "_components": [{"__id__": 275}, {"__id__": 276}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 672, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ffYx/dPjtHypxHvkLR70Ov"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 274}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d7zjQ9tBZDToXqNuoeC7wv"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 274}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "72Ea/iLaZMfIRRBqcnprCL"}, {"__type__": "cc.Node", "_name": "Sprite-011", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 243}, "_children": [], "_active": true, "_components": [{"__id__": 278}, {"__id__": 279}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 736, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "bfAOTSuQRKipc22xcw3E7l"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 277}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "cbO1pfQfhCLJhcjWcM8d3f"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 277}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "33MDkZekZJmY2lccfZFQPw"}, {"__type__": "cc.Node", "_name": "Sprite-012", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 243}, "_children": [], "_active": true, "_components": [{"__id__": 281}, {"__id__": 282}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 800, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "3abFrPHstMMIfKUEJqjytq"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 280}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "19mSzPQ+pBo5YFncrN+/sG"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 280}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "e4798gLfVImLuEVmGl4+5D"}, {"__type__": "cc.Node", "_name": "Sprite-013", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 243}, "_children": [], "_active": true, "_components": [{"__id__": 284}, {"__id__": 285}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 864, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "606IcKochCTbWQKdOwSc/F"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 283}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "56Xd6iHRpMFoNcBoN7WW5U"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 283}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "44WADO0+dOT5jtxKt/lgDh"}, {"__type__": "cc.Node", "_name": "Sprite-014", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 243}, "_children": [], "_active": true, "_components": [{"__id__": 287}, {"__id__": 288}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 928, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e7SVSrxZFKL5g3m9b/rm8g"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 286}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a5Hz2ET8NAl4Ncnv5Zayou"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 286}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "98Vs/a1eNLgYdBrGM2SfHe"}, {"__type__": "cc.Node", "_name": "Sprite-015", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 243}, "_children": [], "_active": true, "_components": [{"__id__": 290}, {"__id__": 291}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 992, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c4scMd1oFA5Iu8+x05nlFv"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 289}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "26uBkG6j9GfK/PeW/p/sD5"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 289}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "43d5LnH0hGg7xPOIZuSWNI"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 243}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": "60MWtjk9ZNEI581gfmmeJO"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 243}, "_enabled": true, "__prefab": null, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": true, "_id": "d2P1zcg95GercacKTMy+Sr"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 243}, "_enabled": true, "__prefab": null, "_alignFlags": 8, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "30w8BXuFFMtYJIHxpSse86"}, {"__type__": "cc.Node", "_name": "DownRoad", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 220}, "_children": [{"__id__": 296}, {"__id__": 299}, {"__id__": 302}, {"__id__": 305}, {"__id__": 308}, {"__id__": 311}, {"__id__": 314}, {"__id__": 317}, {"__id__": 320}, {"__id__": 323}, {"__id__": 326}, {"__id__": 329}, {"__id__": 332}, {"__id__": 335}, {"__id__": 338}, {"__id__": 341}], "_active": true, "_components": [{"__id__": 344}, {"__id__": 345}, {"__id__": 346}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -360, "y": 100, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "cfRwzNvGtBoJd76Y9vhN31"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 295}, "_children": [], "_active": true, "_components": [{"__id__": 297}, {"__id__": 298}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 32, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 1, "w": 6.123233995736766e-17}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 180}, "_id": "55plVaepRAnr5Y/c8HzJ9k"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 296}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "8fUt6Zs7dArY+PX8pCrb3D"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 296}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "efBDnar/tLf7lPxXbmI67W"}, {"__type__": "cc.Node", "_name": "Sprite-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 295}, "_children": [], "_active": true, "_components": [{"__id__": 300}, {"__id__": 301}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 96, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 1, "w": 6.123233995736766e-17}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 180}, "_id": "1fgegWGwtITJUkcrRKQK8Y"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 299}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c2dZiqphhDEZ79s56y2hCG"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 299}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "61VJiHurNMEZiY/BLrBxMm"}, {"__type__": "cc.Node", "_name": "Sprite-002", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 295}, "_children": [], "_active": true, "_components": [{"__id__": 303}, {"__id__": 304}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 160, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 1, "w": 6.123233995736766e-17}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 180}, "_id": "82HRmD4ZBOSKSUoh0m//PA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 302}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "08wg6rkglPJrp9gLvEzo+t"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 302}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "0dDj9zCvBEH661sHzPDDzW"}, {"__type__": "cc.Node", "_name": "Sprite-003", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 295}, "_children": [], "_active": true, "_components": [{"__id__": 306}, {"__id__": 307}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 224, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 1, "w": 6.123233995736766e-17}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 180}, "_id": "ebEB6xQwZJHqdfnl2zg8kR"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 305}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "cfEYcZ1BpN2arWvTLjjweo"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 305}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "55oR50G4BCdanWHEg/Fkj4"}, {"__type__": "cc.Node", "_name": "Sprite-004", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 295}, "_children": [], "_active": true, "_components": [{"__id__": 309}, {"__id__": 310}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 288, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 1, "w": 6.123233995736766e-17}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 180}, "_id": "2f3boSePtLaqtsEMPkyyEa"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 308}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e0zdjQOXpNdaYWobF0vIld"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 308}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "8bSYtHkK1EG6Fyg+d0Khnu"}, {"__type__": "cc.Node", "_name": "Sprite-005", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 295}, "_children": [], "_active": true, "_components": [{"__id__": 312}, {"__id__": 313}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 352, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 1, "w": 6.123233995736766e-17}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 180}, "_id": "cdaCfjSbJNtLenb4nmVjrC"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 311}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "cbXiAmndJKvpb07/krMDUW"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 311}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "c8prFR7pdAXqzXllgb99pz"}, {"__type__": "cc.Node", "_name": "Sprite-006", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 295}, "_children": [], "_active": true, "_components": [{"__id__": 315}, {"__id__": 316}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 416, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 1, "w": 6.123233995736766e-17}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 180}, "_id": "355RE3RaRBLaNyR5EiVt7t"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 314}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "8aehQLNe5MErSy9GOjFMC+"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 314}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "5ecXl2cXdIJKmFQM0Wj2xq"}, {"__type__": "cc.Node", "_name": "Sprite-007", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 295}, "_children": [], "_active": true, "_components": [{"__id__": 318}, {"__id__": 319}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 480, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 1, "w": 6.123233995736766e-17}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 180}, "_id": "42xMuq1FZM0rCt+HYR3tGe"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 317}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1aAnziaetHUKdKZRsZxDIl"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 317}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "dcGrquNM5NUJrFb7bxx4nA"}, {"__type__": "cc.Node", "_name": "Sprite-008", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 295}, "_children": [], "_active": true, "_components": [{"__id__": 321}, {"__id__": 322}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 544, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 1, "w": 6.123233995736766e-17}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 180}, "_id": "9ftg+wHLFBM7G182N71EK6"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 320}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "4cGZgFxatMi7hEi1xVhcJl"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 320}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "e6nMcnyqxEsZnGrwkgwwCu"}, {"__type__": "cc.Node", "_name": "Sprite-009", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 295}, "_children": [], "_active": true, "_components": [{"__id__": 324}, {"__id__": 325}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 608, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 1, "w": 6.123233995736766e-17}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 180}, "_id": "5c19xPVrVLH4X0ouho+GQz"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 323}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f6uGx3BVhC0Z+l17w2tawb"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 323}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "2cwSaiWZ1F64Z7wYxvnLg0"}, {"__type__": "cc.Node", "_name": "Sprite-010", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 295}, "_children": [], "_active": true, "_components": [{"__id__": 327}, {"__id__": 328}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 672, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 1, "w": 6.123233995736766e-17}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 180}, "_id": "745eeHAc9DxZUSolEDs2xk"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 326}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "2aJ10thntKHYoh0h/cVKEA"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 326}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "ddoWmhuGZPsKSzIEVEqhyC"}, {"__type__": "cc.Node", "_name": "Sprite-011", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 295}, "_children": [], "_active": true, "_components": [{"__id__": 330}, {"__id__": 331}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 736, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 1, "w": 6.123233995736766e-17}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 180}, "_id": "a8CcoSdNFGwrGSHwBdl+3y"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 329}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a3LK/ffh1AjKCKrB714hiH"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 329}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "79l1FZxlpG2qR4DhMmQC/8"}, {"__type__": "cc.Node", "_name": "Sprite-012", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 295}, "_children": [], "_active": true, "_components": [{"__id__": 333}, {"__id__": 334}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 800, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 1, "w": 6.123233995736766e-17}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 180}, "_id": "62KKJEQF5N5I3iCiQ5XJw1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 332}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "15K97xoOJAy6Sed4xmST0d"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 332}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "30rgl9TkhHAZFudDKepVFF"}, {"__type__": "cc.Node", "_name": "Sprite-013", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 295}, "_children": [], "_active": true, "_components": [{"__id__": 336}, {"__id__": 337}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 864, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 1, "w": 6.123233995736766e-17}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 180}, "_id": "76TTbVcA5KIqh8gTqc8WiS"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 335}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a8mkXYS6dKHrFy91rWNrZb"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 335}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "71VT9MIm1IGIqDwB9c7THl"}, {"__type__": "cc.Node", "_name": "Sprite-014", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 295}, "_children": [], "_active": true, "_components": [{"__id__": 339}, {"__id__": 340}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 928, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 1, "w": 6.123233995736766e-17}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 180}, "_id": "39Uf3lhHlH1pXhP1xFfQ5T"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 338}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "2eWGmPQZ9OT5wxhaFgLSOs"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 338}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "81TVMbbuBO1aziMsSYWtsC"}, {"__type__": "cc.Node", "_name": "Sprite-015", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 295}, "_children": [], "_active": true, "_components": [{"__id__": 342}, {"__id__": 343}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 992, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 1, "w": 6.123233995736766e-17}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 180}, "_id": "52RGuU7xBJ/IvCeO4yBf+o"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 341}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b9om+bdtdMqp2bUWo1sf3n"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 341}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c5148d7-cc03-497f-8553-e1befd429437@eebd5", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "50KZ0aVwJMA4YDughrPScZ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 295}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": "36fw6uidVNsJcCVYv2aibQ"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 295}, "_enabled": true, "__prefab": null, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": true, "_id": "5cwdxtyd9EgaMG58SwYEo9"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 295}, "_enabled": true, "__prefab": null, "_alignFlags": 8, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "fehrcdx3lM963lzl+zfflr"}, {"__type__": "cc.Node", "_name": "Box", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 220}, "_children": [], "_active": true, "_components": [{"__id__": 348}, {"__id__": 349}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 502.956, "y": -40.716, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "7fAJwhkzlPJprulBVFnYQ9"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 347}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a2vbNgjLFAvrcwZH+ekd9d"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 347}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "91fa3bf8-8d3d-428e-b673-53cc7a0006ab@41580", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "10QUkG+WpMYa7h77Uphl/u"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 220}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f1yY7N3LJAYplun7KIlTCJ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 220}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": -2.7250000000000054, "_right": 2.7250000000000054, "_top": 2.724999999999963, "_bottom": -2.724999999999963, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": "96WblUOyNF54mJBv2KuPsb"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d6rUX5yfhMlKoWX2bSbawx"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_cameraComponent": {"__id__": 12}, "_alignCanvasWithScreen": true, "_id": "12O/ljcVlEqLmVm3U2gEOQ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 5.684341886080802e-14, "_bottom": 5.684341886080802e-14, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "c5V1EV8IpMtrIvY1OE9t2u"}, {"__type__": "cc.PrefabInfo", "root": null, "asset": null, "fileId": "e4c55267-f76b-48d0-8e02-efd465efcaf7", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 3}, {"__id__": 200}, {"__id__": 76}, {"__id__": 94}, {"__id__": 110}, {"__id__": 126}, {"__id__": 145}, {"__id__": 161}, {"__id__": 177}]}, {"__type__": "cc.SceneGlobals", "ambient": {"__id__": 357}, "shadows": {"__id__": 358}, "_skybox": {"__id__": 359}, "fog": {"__id__": 360}, "octree": {"__id__": 361}, "skin": {"__id__": 362}, "lightProbeInfo": {"__id__": 363}, "postSettings": {"__id__": 364}, "bakedWithStationaryMainLight": false, "bakedWithHighpLightmap": false}, {"__type__": "cc.AmbientInfo", "_skyColorHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyColor": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyIllumHDR": 20000, "_skyIllum": 20000, "_groundAlbedoHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_groundAlbedo": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_skyColorLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 1}, "_skyIllumLDR": 20000, "_groundAlbedoLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}}, {"__type__": "cc.ShadowsInfo", "_enabled": false, "_type": 0, "_normal": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_distance": 0, "_planeBias": 1, "_shadowColor": {"__type__": "cc.Color", "r": 76, "g": 76, "b": 76, "a": 255}, "_maxReceived": 4, "_size": {"__type__": "cc.Vec2", "x": 512, "y": 512}}, {"__type__": "cc.SkyboxInfo", "_envLightingType": 0, "_envmapHDR": null, "_envmap": null, "_envmapLDR": null, "_diffuseMapHDR": null, "_diffuseMapLDR": null, "_enabled": false, "_useHDR": true, "_editableMaterial": null, "_reflectionHDR": null, "_reflectionLDR": null, "_rotationAngle": 0}, {"__type__": "cc.FogInfo", "_type": 0, "_fogColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_enabled": false, "_fogDensity": 0.3, "_fogStart": 0.5, "_fogEnd": 300, "_fogAtten": 5, "_fogTop": 1.5, "_fogRange": 1.2, "_accurate": false}, {"__type__": "cc.OctreeInfo", "_enabled": false, "_minPos": {"__type__": "cc.Vec3", "x": -1024, "y": -1024, "z": -1024}, "_maxPos": {"__type__": "cc.Vec3", "x": 1024, "y": 1024, "z": 1024}, "_depth": 8}, {"__type__": "cc.SkinInfo", "_enabled": false, "_blurRadius": 0.01, "_sssIntensity": 3}, {"__type__": "cc.LightProbeInfo", "_giScale": 1, "_giSamples": 1024, "_bounces": 2, "_reduceRinging": 0, "_showProbe": true, "_showWireframe": true, "_showConvex": false, "_data": null, "_lightProbeSphereVolume": 1}, {"__type__": "cc.PostSettingsInfo", "_toneMappingType": 0}]