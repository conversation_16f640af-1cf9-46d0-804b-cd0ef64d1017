[{"__type__": "cc.SceneAsset", "_name": "game", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_name": "game", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [], "_prefab": {"__id__": 570}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "autoReleaseAssets": true, "_globals": {"__id__": 571}, "_id": "25c62414-a7d4-47e6-b3d9-31f507797fc1"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 11}, {"__id__": 13}, {"__id__": 15}, {"__id__": 552}, {"__id__": 555}, {"__id__": 565}, {"__id__": 20}], "_active": true, "_components": [{"__id__": 567}, {"__id__": 568}, {"__id__": 569}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 360, "y": 640, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "beI88Z2HpFELqR4T5EMHpg"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 2}, "_prefab": {"__id__": 4}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 3}, "asset": {"__uuid__": "f037cad0-306f-45ef-9196-d43452072272", "__expectedType__": "cc.Prefab"}, "fileId": "fdxwQqRvtNvYaWaUumVqD2", "instance": {"__id__": 5}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "39ecgS3/5Fd5Gmjae9+2VU", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 6}, {"__id__": 8}, {"__id__": 9}, {"__id__": 10}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 7}, "propertyPath": ["_name"], "value": "CommonAssets"}, {"__type__": "cc.TargetInfo", "localID": ["fdxwQqRvtNvYaWaUumVqD2"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 7}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 7}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 7}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_name": "MainCamera", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 12}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ebFwiq8gBFaYpqYbdoDODe"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 2, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 640, "_near": 0, "_far": 1000, "_color": {"__type__": "cc.Color", "r": 31, "g": 30, "b": 30, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 7, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 1073741824, "_targetTexture": null, "_postProcess": null, "_usePostProcess": false, "_cameraType": -1, "_trackingType": 0, "_id": "63WIch3o5BEYRlXzTT0oWc"}, {"__type__": "cc.Node", "_name": "UICamera", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 14}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "faFD2HN39AooSxweIkDER1"}, {"__type__": "cc.Camera", "_name": "Camera<CameraComponent>", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 3, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 640, "_near": 0, "_far": 1000, "_color": {"__type__": "cc.Color", "r": 15, "g": 14, "b": 14, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 6, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 33554432, "_targetTexture": null, "_postProcess": null, "_usePostProcess": false, "_cameraType": -1, "_trackingType": 0, "_id": "6azCdqkYlB+Yic9A/zx8zA"}, {"__type__": "cc.Node", "_name": "GameManager", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 16}, {"__id__": 17}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "485Y6xAN9LsIpk3EL/tIkt"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 15}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "293ScbKbZIOY3hdaQmrd6Z"}, {"__type__": "fd7d1CzSZFJrZ09qgecGvJy", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 15}, "_enabled": true, "__prefab": null, "mainCamera": {"__id__": 12}, "uiCamera": {"__id__": 14}, "gameoverMaskFab": {"__uuid__": "4675d05d-9649-416a-9e06-ac4caba809a2", "__expectedType__": "cc.Prefab"}, "backpackItem": {"__uuid__": "ebba3783-235e-4cbb-9b86-508d1e83fa9b", "__expectedType__": "cc.Prefab"}, "backpack": {"__id__": 18}, "backpackTwo": {"__id__": 431}, "coinNode": {"__id__": 269}, "coinItem": {"__uuid__": "ed9b679a-1046-46b2-9445-98216fc80343", "__expectedType__": "cc.Prefab"}, "backpackS": {"__uuid__": "c9280fa7-7911-4fcd-bab1-fba366238dc6", "__expectedType__": "cc.Prefab"}, "frame19": {"__uuid__": "91fa3bf8-8d3d-428e-b673-53cc7a0006ab@e4d93", "__expectedType__": "cc.SpriteFrame"}, "frame20": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@ac77e", "__expectedType__": "cc.SpriteFrame"}, "frame26": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@fc261", "__expectedType__": "cc.SpriteFrame"}, "frame31": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@f5077", "__expectedType__": "cc.SpriteFrame"}, "frame30": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@f350a", "__expectedType__": "cc.SpriteFrame"}, "frame25": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@2e3d0", "__expectedType__": "cc.SpriteFrame"}, "frame42": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@386e8", "__expectedType__": "cc.SpriteFrame"}, "frame45": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@0917f", "__expectedType__": "cc.SpriteFrame"}, "frame36": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@b01e2", "__expectedType__": "cc.SpriteFrame"}, "frame28": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@cbc69", "__expectedType__": "cc.SpriteFrame"}, "frame34": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@cfb42", "__expectedType__": "cc.SpriteFrame"}, "frame48": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@a223d", "__expectedType__": "cc.SpriteFrame"}, "frame50": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@05497", "__expectedType__": "cc.SpriteFrame"}, "frame46": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@c4c1a", "__expectedType__": "cc.SpriteFrame"}, "frame44": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@c4f99", "__expectedType__": "cc.SpriteFrame"}, "frame24": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@7a5c6", "__expectedType__": "cc.SpriteFrame"}, "frame27": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@a3e24", "__expectedType__": "cc.SpriteFrame"}, "frame35": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@61c6f", "__expectedType__": "cc.SpriteFrame"}, "frame37": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@e575f", "__expectedType__": "cc.SpriteFrame"}, "frame38": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@3690b", "__expectedType__": "cc.SpriteFrame"}, "frame39": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@01e41", "__expectedType__": "cc.SpriteFrame"}, "frame41": {"__uuid__": "466053be-5823-4da4-b022-604a60a5f372@330b3", "__expectedType__": "cc.SpriteFrame"}, "itemFloatUpPrefab": {"__uuid__": "9eb4ef7a-8cd5-47a9-b798-967e7033e635", "__expectedType__": "cc.Prefab"}, "_id": "64FOv4JyRPg5FMmeQdGmmT"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 19}, "_children": [{"__id__": 484}, {"__id__": 502}, {"__id__": 518}, {"__id__": 534}], "_active": true, "_components": [{"__id__": 550}, {"__id__": 551}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 55, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "84zPF+AcVMSIqFGaAeDJEx"}, {"__type__": "cc.Node", "_name": "Backpack", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [{"__id__": 18}, {"__id__": 431}], "_active": false, "_components": [{"__id__": 482}, {"__id__": 483}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 113.38, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "36ndwKchdCxrOzXqjFBpP5"}, {"__type__": "cc.Node", "_name": "UI", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 21}, {"__id__": 25}, {"__id__": 39}, {"__id__": 55}, {"__id__": 111}, {"__id__": 117}, {"__id__": 122}, {"__id__": 163}, {"__id__": 269}, {"__id__": 279}, {"__id__": 289}, {"__id__": 298}, {"__id__": 307}, {"__id__": 339}, {"__id__": 19}, {"__id__": 349}, {"__id__": 371}, {"__id__": 410}, {"__id__": 414}, {"__id__": 425}], "_active": true, "_components": [{"__id__": 428}, {"__id__": 429}, {"__id__": 430}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f6vEMw30xGULD0XMIxxdX1"}, {"__type__": "cc.Node", "_name": "LightGlow", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [], "_active": false, "_components": [{"__id__": 22}, {"__id__": 23}, {"__id__": 24}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "1fJJeojT9NVJg0Tt3eGUka"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 21}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 982, "height": 983}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "790A4VG6ZHLI6yk8pVmRe7"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 21}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e685a85e-f499-4dc3-bab3-57ba4454e98b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "5fhSHCXkRA/J7+F9QVF1nb"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 21}, "_enabled": true, "__prefab": null, "_opacity": 155, "_id": "e3tOXyKoREc7crFIrCY+lY"}, {"__type__": "cc.Node", "_name": "GamePad", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [{"__id__": 26}, {"__id__": 29}, {"__id__": 34}], "_active": true, "_components": [{"__id__": 37}, {"__id__": 38}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "80LAw0k/xI5rLFgMT95fPb"}, {"__type__": "cc.Node", "_name": "Bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 25}, "_children": [], "_active": true, "_components": [{"__id__": 27}, {"__id__": 28}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c8hwN5FnNF3bmGpyEXS74p"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 26}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 280, "height": 280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b7TTXuGyNO0Z5/dAPLtBCi"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 26}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "87e01919-9420-4626-9f66-4bb30feb9690@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "ccJSiyMSJAbYwNAConQj7k"}, {"__type__": "cc.Node", "_name": "Direct", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 25}, "_children": [{"__id__": 30}], "_active": true, "_components": [{"__id__": 33}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b9vptTutNP27I/BEvraFxL"}, {"__type__": "cc.Node", "_name": "Bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 29}, "_children": [], "_active": true, "_components": [{"__id__": 31}, {"__id__": 32}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 6, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "79QgOG3SJDkoyRFsSk6XOW"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 30}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 115, "height": 171}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": "16n19L+LdCapuwIQCYmJme"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 30}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0e8b8531-9873-44a4-912b-95164dfed23c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "a8G7e/jJJEF4h9ED1C+nUn"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 29}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 121, "height": 171}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": "ae+PWcmqdDNZ1HK7Y6XRwt"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 25}, "_children": [], "_active": true, "_components": [{"__id__": 35}, {"__id__": 36}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 2.596, "y": -0.98, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "aeQmtXpx9HbZC9OI+MGxiI"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 34}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 59, "height": 59}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "0cFUgAuPFPDZsCl9LicEoH"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 34}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bc961abe-8492-4eda-ab8c-39cefb85a079@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "665NnmdDJCUpgJEDtaMRce"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 280, "height": 280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "14Ok4Cu8FAZbS6ly4u4YHT"}, {"__type__": "af378D6cTpIaIaVx6xMiJOO", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": null, "from": "game", "_id": "f2sf97ZcZDJ5YfXo+vKoxk"}, {"__type__": "cc.Node", "_name": "Joystick", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [{"__id__": 40}, {"__id__": 43}, {"__id__": 46}, {"__id__": 49}], "_active": false, "_components": [{"__id__": 52}, {"__id__": 53}, {"__id__": 54}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "6b/4f/CzdD6Lwj+MMvw/tX"}, {"__type__": "cc.Node", "_name": "up", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 39}, "_children": [], "_active": true, "_components": [{"__id__": 41}, {"__id__": 42}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d4qVwPDDBDSZ6CUs/vNui8"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 40}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "99Fds+7TNNqLwnv1KQ7Mkl"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 40}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_miterLimit": 10, "_id": "d1o1dMd3VFjLteV9WYLeKl"}, {"__type__": "cc.Node", "_name": "right", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 39}, "_children": [], "_active": true, "_components": [{"__id__": 44}, {"__id__": 45}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ac42MvohtNw7mauia4upB5"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 43}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "7aPUXLb7ZKzbfVZibMsouL"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 43}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_miterLimit": 10, "_id": "23DGoeHVZNPbApT1EIc/F5"}, {"__type__": "cc.Node", "_name": "down", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 39}, "_children": [], "_active": true, "_components": [{"__id__": 47}, {"__id__": 48}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b8mF8YhNNERpukMYA6/SRu"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 46}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "373Azh3s1Gy6VRPkAJfTY+"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 46}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_miterLimit": 10, "_id": "c7jo1ZDXVIy5hEJ78nordE"}, {"__type__": "cc.Node", "_name": "left", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 39}, "_children": [], "_active": true, "_components": [{"__id__": 50}, {"__id__": 51}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "77SCEydfpPVb6xtd524ezH"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 49}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "3e+Sr9jNdGPbArN0wcNHrg"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 49}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_miterLimit": 10, "_id": "camK9XRm5H756wWwTCijeK"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 39}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "bfoTTZDRBFZp9PxG5FDts3"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 39}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_miterLimit": 10, "_id": "ddA6Q2jN5BH7ICrUqyISSh"}, {"__type__": "11b74UZZV5PuKZ8xDUaBBHG", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 39}, "_enabled": true, "__prefab": null, "_id": "9bnYcZPy5EWp64j1Cu1X/6"}, {"__type__": "cc.Node", "_name": "Status", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [{"__id__": 56}, {"__id__": 59}, {"__id__": 62}, {"__id__": 65}, {"__id__": 68}, {"__id__": 79}, {"__id__": 82}, {"__id__": 96}, {"__id__": 99}, {"__id__": 106}], "_active": true, "_components": [{"__id__": 109}, {"__id__": 110}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -139.513, "y": 540, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2e9FweWvJLVpOTuhyU3r/0"}, {"__type__": "cc.Node", "_name": "HPNumBg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 55}, "_children": [], "_active": true, "_components": [{"__id__": 57}, {"__id__": 58}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 31.302, "y": 8.204, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8a/bQ9YxVAp4a0sNZzMOa4"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 56}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 48}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "07HvGTYHVB8JRiLYVzk1j3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 56}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "395c219f-431b-4d53-9573-8c36985528a1@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "a05pLMwwhC0KiF5Mh2ghkP"}, {"__type__": "cc.Node", "_name": "Life_small", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 55}, "_children": [], "_active": true, "_components": [{"__id__": 60}, {"__id__": 61}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -21.397, "y": 9.218, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "5763zMlf5L/ZsOEobdpeMC"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 59}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 53.489000000000004, "height": 47.35711366791369}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "41hqrCSF9JzJNnXrL8B3pd"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 59}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "07cdee35-e599-4426-abd4-e4da77edad95@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "b5NX0QbkBNeLtWybhKEs+X"}, {"__type__": "cc.Node", "_name": "HPNum", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 55}, "_children": [], "_active": true, "_components": [{"__id__": 63}, {"__id__": 64}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 21.892, "y": 10.055, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "dd1p5Urn5NyKj1hOycSP0k"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 62}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 56.11717224121094, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": "70hbvS59NN1ZOEfxXQA8p+"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 62}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "10", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 47.8, "_fontSize": 47.8, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a02df6db-4f52-4023-8bfb-6429098d02cf", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 128, "g": 14, "b": 36, "a": 255}, "_outlineWidth": 3.9, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "69nIjq1eJBn5EmjdUncooO"}, {"__type__": "cc.Node", "_name": "LampNumBg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 55}, "_children": [], "_active": true, "_components": [{"__id__": 66}, {"__id__": 67}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 229.568, "y": 8.204, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "344nwNzTNAYKKVCbpWE7kO"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 65}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 48}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "78+I+iqclKzaMKboeUPMpA"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 65}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "395c219f-431b-4d53-9573-8c36985528a1@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "e84ziEOdRLAZXmfAXWj5Mx"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 55}, "_prefab": {"__id__": 69}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 68}, "asset": {"__uuid__": "4a8ca964-7e8b-465d-8787-cc26724b90a0", "__expectedType__": "cc.Prefab"}, "fileId": "ecEBNpoHBFWaW5xHYETV4k", "instance": {"__id__": 70}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "66UkTs2JBBQ474IDd63VcQ", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 71}, {"__id__": 73}, {"__id__": 74}, {"__id__": 75}, {"__id__": 76}, {"__id__": 77}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 72}, "propertyPath": ["_name"], "value": "LampIcon"}, {"__type__": "cc.TargetInfo", "localID": ["ecEBNpoHBFWaW5xHYETV4k"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 72}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 175.342, "y": 10.72, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 72}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 72}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 72}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 78}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 68.412, "height": 57.828782}}, {"__type__": "cc.TargetInfo", "localID": ["cdcZKUH5tIVrD6i/MQGVAB"]}, {"__type__": "cc.Node", "_name": "LampNum", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 55}, "_children": [], "_active": true, "_components": [{"__id__": 80}, {"__id__": 81}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 211.033, "y": 10.055, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "27b0YL6DpDWKFBex/DNAaH"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 79}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 122.55915832519531, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": "05PIR76YJHjqmNLzOl0PMo"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 79}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "5000", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 47.8, "_fontSize": 47.8, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a02df6db-4f52-4023-8bfb-6429098d02cf", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 119, "g": 80, "b": 21, "a": 255}, "_outlineWidth": 3.9, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "15i8OeC9lOBZ81SaaAd1mw"}, {"__type__": "cc.Node", "_name": "TargetArea", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 55}, "_children": [{"__id__": 83}, {"__id__": 86}, {"__id__": 89}, {"__id__": 92}], "_active": true, "_components": [{"__id__": 95}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 137.322, "y": -165.68, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "11z59GXllKfY93nZmwc0R6"}, {"__type__": "cc.Node", "_name": "Bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 82}, "_children": [], "_active": true, "_components": [{"__id__": 84}, {"__id__": 85}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "73oe8HO/tFe55mlSjQOhQz"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 83}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 180}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "8fzHSWrhlI1ra5VHitgx67"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 83}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "4a08a36c-4169-43a6-88fc-c87968060b17@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "09FvvC4oNBHaoSHtiQhxnn"}, {"__type__": "cc.Node", "_name": "Title", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 82}, "_children": [], "_active": true, "_components": [{"__id__": 87}, {"__id__": 88}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 55.296, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "97YLXkj1NBoLOO072dld9e"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 86}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 71.79994201660156, "height": 25.2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "49+zR5e65ONYDqT2GzymYK"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 86}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Mission", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "", "_lineHeight": 20, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a02df6db-4f52-4023-8bfb-6429098d02cf", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "6838a1VX9BPKQhfEN/N+aJ"}, {"__type__": "cc.Node", "_name": "Layout", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 82}, "_children": [], "_active": true, "_components": [{"__id__": 90}, {"__id__": 91}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -10, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "15LlZ4EEJN9b04Mg64llS8"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 89}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": -4, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "65vRYzT2NCir9ivHJIWza9"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 89}, "_enabled": true, "__prefab": null, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 4, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": "debrj4bhxIRb0vNrOh2t5v"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 82}, "_children": [], "_active": false, "_components": [{"__id__": 93}, {"__id__": 94}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -58.678, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "43BfloYP1FqKmXq1hcy8Ct"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 92}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 360, "height": 45.199999999999996}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "02W+pRIHJGcbroFo4/fP+j"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 92}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 194, "g": 194, "b": 194, "a": 255}, "_string": "* Gold ore below 100 levels; Iron ore below 200 levels;", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 20, "_overflow": 3, "_enableWrapText": true, "_font": {"__uuid__": "31048cba-04e1-4b31-97cb-1583b06cb195", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "e92gDQcnFC7YzV/9aG1GaT"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 82}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 36}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "ccdxM93Z9KlK7UR2Nns1Ig"}, {"__type__": "cc.Node", "_name": "Rock_08", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 55}, "_children": [], "_active": true, "_components": [{"__id__": 97}, {"__id__": 98}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -34, "y": -300.335, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8cWLgD6ktPtZ5bH2LV5xfh"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 96}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 46.46, "height": 41.81954812514926}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "4c1dG9f+JH85mNXNr/4bt8"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 96}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "91fa3bf8-8d3d-428e-b673-53cc7a0006ab@e4d93", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "9eYD58aNJExrnu1KZP2058"}, {"__type__": "cc.Node", "_name": "ExpBar", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 55}, "_children": [{"__id__": 100}], "_active": true, "_components": [{"__id__": 103}, {"__id__": 104}, {"__id__": 105}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 153.187, "y": -300.192, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "09te2os35GZpc9H877gbv7"}, {"__type__": "cc.Node", "_name": "Bar", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 99}, "_children": [], "_active": true, "_components": [{"__id__": 101}, {"__id__": 102}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 159.688, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "27mMdu8zxK3JKKwnSUDTrr"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 100}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 320, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_id": "4bTFPZiqZO+pf8YDnvk9ai"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 100}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_spriteFrame": {"__uuid__": "24a704da-2867-446d-8d1a-5e920c75e09d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 1, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "5fwtQudWJMEaMmoogupQSw"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 99}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 320, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c6Nc6fL1ZH4Z76XcEJUOGd"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 99}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 8, "g": 143, "b": 35, "a": 255}, "_spriteFrame": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "c5ph+IJUBKx6F6ZpFKUU06"}, {"__type__": "cc.ProgressBar", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 99}, "_enabled": true, "__prefab": null, "_barSprite": {"__id__": 102}, "_mode": 0, "_totalLength": 300, "_progress": 0.5, "_reverse": true, "_id": "dbwLNRh3VLvJXY9NZqOq7r"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 55}, "_children": [], "_active": true, "_components": [{"__id__": 107}, {"__id__": 108}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -46.736, "y": -333.666, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2coE7r/OdPcZ70aDZ+gWMX"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 106}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 110}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_id": "892vHPnydIzrATLIetgTBs"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 106}, "_enabled": true, "__prefab": null, "_resizeMode": 0, "_layoutType": 3, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 8, "_spacingY": 4, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 2, "_constraintNum": 6, "_affectedByScale": false, "_isAlign": false, "_id": "78p8Oqy4xHvoiBd3gIsxxl"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 55}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_id": "94cZ3RtllBT6yI1pTl1gcj"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 55}, "_enabled": true, "__prefab": null, "_alignFlags": 9, "_target": {"__id__": 2}, "_left": 220.487, "_right": 0, "_top": 100, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "62/vF9LKRMOa2Ji9MUOf3v"}, {"__type__": "cc.Node", "_name": "Bag", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [{"__id__": 112}], "_active": false, "_components": [{"__id__": 115}, {"__id__": 116}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 460, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "1dsf/C4FVOPJlqGJk2NVhw"}, {"__type__": "cc.Node", "_name": "Money", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 111}, "_children": [], "_active": true, "_components": [{"__id__": 113}, {"__id__": 114}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "beEHH4sxlJXLhvjhv4ZJ+4"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 112}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 115.23046875, "height": 67}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "54+R1qw1JIsIPd+3O7ZVyG"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 112}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "0000", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 50, "_fontSize": 50, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 50, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 100, "g": 100, "b": 100, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "cdFBFy8g9P67cbYgwf0tKF"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 111}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "6es+d2wcZIYJEhC2qBDKgF"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 111}, "_enabled": true, "__prefab": null, "_alignFlags": 17, "_target": {"__id__": 2}, "_left": 0, "_right": 0, "_top": 130, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "a9hZa3yeRKmI987eq/78HP"}, {"__type__": "cc.Node", "_name": "Setting", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [], "_active": true, "_components": [{"__id__": 118}, {"__id__": 119}, {"__id__": 120}, {"__id__": 121}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 267.36, "y": 543.508, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "edH1wtKrdJMpMhdfhbDBxJ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 117}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 73, "height": 83}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e9SBffikVKYrZrQdjK75Pz"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 117}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "29a667f8-d27e-4109-8bc1-a0d53a520406@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "abqY5TikBBHJNeJvdsoK43"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 117}, "_enabled": true, "__prefab": null, "_alignFlags": 33, "_target": null, "_left": 0, "_right": 56.139999999999986, "_top": 54.99199999999996, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "6bGBY6LQFLfou9dOQUnMJ6"}, {"__type__": "2c6c1EcL4tOKaZk0evbteAO", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 117}, "_enabled": true, "__prefab": null, "GameSettingPrefab": {"__uuid__": "de2d0a77-bc9e-4778-81c0-303a64b76e60", "__expectedType__": "cc.Prefab"}, "_id": "ddRBMMflBLXbuV1O913f0V"}, {"__type__": "cc.Node", "_name": "Level", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [{"__id__": 123}, {"__id__": 138}, {"__id__": 151}, {"__id__": 154}], "_active": true, "_components": [{"__id__": 161}, {"__id__": 162}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 270, "y": 270, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "031tC3PmpJcZ/CtPV94cLX"}, {"__type__": "cc.Node", "_name": "ProgressBar", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 122}, "_children": [{"__id__": 124}], "_active": true, "_components": [{"__id__": 135}, {"__id__": 136}, {"__id__": 137}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -75, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9bsjlc8BxH3bbQcEPcp2Y5"}, {"__type__": "cc.Node", "_name": "Bar", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 123}, "_children": [{"__id__": 125}], "_active": true, "_components": [{"__id__": 133}, {"__id__": 134}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 125, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "84Rd3Hl7BEGq4ZP4QUF45s"}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 124}, "_children": [{"__id__": 126}, {"__id__": 129}], "_active": true, "_components": [{"__id__": 132}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f30HTp6UFIRJrY63TVoOlq"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 125}, "_children": [], "_active": true, "_components": [{"__id__": 127}, {"__id__": 128}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 9.546, "y": 9.251, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e7Q6f6+FxIWKmG3q8xPxGZ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 126}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "cc5OS1QbBLcIHsfgt4nnU+"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 126}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "94f4492d-baa3-4c16-a982-98b209ad80fe@7b9c6", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "d5LIw5M+JGwpbxwyEdT17Q"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 125}, "_children": [], "_active": true, "_components": [{"__id__": 130}, {"__id__": 131}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9eb0vNo6FFGIEE8FhwaSmO"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 129}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 59.48796081542969, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "961/yidOlKY7WEgFu6l6yS"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 129}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "label", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a02df6db-4f52-4023-8bfb-6429098d02cf", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "0flQSTe7JLWKaJJMRPnWTA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 125}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "7aBnswxUBIqZa8WLA1q+2J"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 124}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 20, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": "a9QlPOBZBF4IeUsxYwvA2/"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 124}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 131, "g": 62, "b": 54, "a": 255}, "_spriteFrame": null, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 1, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "c91j0v2BxNgq/9OXajKOvP"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 20, "height": 350}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "16v8Ofp59KzKyAaYvKUNrX"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 131, "g": 62, "b": 54, "a": 255}, "_spriteFrame": {"__uuid__": "24a704da-2867-446d-8d1a-5e920c75e09d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "659f2Jum1CaKfFdVjmmiHQ"}, {"__type__": "cc.ProgressBar", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": null, "_barSprite": {"__id__": 134}, "_mode": 1, "_totalLength": 250, "_progress": 0, "_reverse": true, "_id": "1ctv//weZHNJXzhmEIpG5d"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 122}, "_children": [{"__id__": 139}, {"__id__": 142}, {"__id__": 145}], "_active": true, "_components": [{"__id__": 148}, {"__id__": 149}, {"__id__": 150}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 125, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "0d2YtSSupKCK2tEj4BhsI9"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 138}, "_children": [], "_active": true, "_components": [{"__id__": 140}, {"__id__": 141}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d7ePRNSVVD9p20cIhU6/uU"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 139}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 59.03996276855469, "height": 41.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "92nHYyh+RFkpuXZGxaUaR+"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 139}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "De<PERSON><PERSON>", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a02df6db-4f52-4023-8bfb-6429098d02cf", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "29JThcX+tMDL7PelYH3i0K"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 138}, "_children": [], "_active": false, "_components": [{"__id__": 143}, {"__id__": 144}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -55, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9fSo+rIpBCBKhaYMVEAle1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 142}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 25, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "40DmvTRDJAaKq5NCC8vvIB"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 142}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 78, "g": 41, "b": 46, "a": 255}, "_spriteFrame": {"__uuid__": "9fd900dd-221b-4f89-8f2c-fba34243c835@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "d3IhCwGV9MxbClc+7Z2+J+"}, {"__type__": "cc.Node", "_name": "Sprite-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 138}, "_children": [], "_active": false, "_components": [{"__id__": 146}, {"__id__": 147}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -55, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9enVCxChZIUqEn2iDm2qiX"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 145}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 25, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c8BNJc9SVBtbUsnG1O9c+Q"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 145}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 131, "g": 62, "b": 54, "a": 255}, "_spriteFrame": {"__uuid__": "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "8aPpI4tQ5Ay7sYM4NVIYtK"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 138}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b9p/Dw4uxOo6kVHQ8XVmu0"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 138}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_miterLimit": 10, "_id": "adv7KJihpPCJPstMmHhyOS"}, {"__type__": "b8f5dSGUuhCKoA6BZ9MEo6K", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 138}, "_enabled": true, "__prefab": null, "_id": "050B5cMLJMY7jPx86Y+sfY"}, {"__type__": "cc.Node", "_name": "Sprite-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 122}, "_children": [], "_active": false, "_components": [{"__id__": 152}, {"__id__": 153}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -215, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "cbLxyerRJFH6PDZ33a+ziY"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 151}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 25, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "dfnAL3BnJNDJV/f7QK9FXS"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 151}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 131, "g": 62, "b": 54, "a": 255}, "_spriteFrame": {"__uuid__": "24a704da-2867-446d-8d1a-5e920c75e09d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "31tC6hKkJLMIMrdfpY7Tfg"}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 122}, "_children": [{"__id__": 155}], "_active": true, "_components": [{"__id__": 158}, {"__id__": 159}, {"__id__": 160}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -275.512, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "eaRAvdQV1HkrGJXghHJrPq"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 154}, "_children": [], "_active": true, "_components": [{"__id__": 156}, {"__id__": 157}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "dbAkzf6xFPVp3whSJ5pmLb"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 155}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 38.67999267578125, "height": 41.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b460E4k1hIiK2GNmU3lWyz"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 155}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "255", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a02df6db-4f52-4023-8bfb-6429098d02cf", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "d2Eu7PgklI6r2tFNSFGfAb"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 154}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a9RElAjstGeLIQVyYmxMpB"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 154}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_miterLimit": 10, "_id": "3294r25ctO7JZJUZcsql9z"}, {"__type__": "b8f5dSGUuhCKoA6BZ9MEo6K", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 154}, "_enabled": true, "__prefab": null, "_id": "e8lj6sfYtLQI2E9Bjhxr17"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 122}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 300}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1c21NzfNJKdp3jaFtRpsRC"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 122}, "_enabled": true, "__prefab": null, "_alignFlags": 33, "_target": {"__id__": 2}, "_left": 0, "_right": 40, "_top": 220, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "61ws5YguRLvKwOhBLf3kBJ"}, {"__type__": "cc.Node", "_name": "Prop", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [{"__id__": 164}, {"__id__": 188}, {"__id__": 214}, {"__id__": 240}], "_active": false, "_components": [{"__id__": 266}, {"__id__": 267}, {"__id__": 268}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 200, "y": -315, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4fY/zFrkpBzbHIHU5hMpKm"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 163}, "_prefab": {"__id__": 165}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 164}, "asset": {"__uuid__": "6a584c2c-5d3f-41b0-ae66-ac6fbfef7ebe", "__expectedType__": "cc.Prefab"}, "fileId": "d2Djdc8BdFaJQR2Fh3pjGd", "instance": {"__id__": 166}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "ca0kPrgZpE6KckHSK49vTH", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 167}, {"__id__": 169}, {"__id__": 170}, {"__id__": 171}, {"__id__": 172}, {"__id__": 174}, {"__id__": 176}, {"__id__": 178}, {"__id__": 180}, {"__id__": 181}, {"__id__": 183}, {"__id__": 184}, {"__id__": 186}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 168}, "propertyPath": ["_name"], "value": "flyBoom"}, {"__type__": "cc.TargetInfo", "localID": ["d2Djdc8BdFaJQR2Fh3pjGd"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 168}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 80, "y": 180, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 168}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 168}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 173}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 100, "height": 100}}, {"__type__": "cc.TargetInfo", "localID": ["b74vQzPe1PvLyxwAcSQXup"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 175}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 100, "height": 40}}, {"__type__": "cc.TargetInfo", "localID": ["974/EebCxB2KRNFmWXkN/l"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 177}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -23, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["4eLfuzlNVFVp71AnntRWSk"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 179}, "propertyPath": ["title"], "value": "窜天手雷"}, {"__type__": "cc.TargetInfo", "localID": ["1ezry6dQhI3K85jLuFKluq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 168}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 182}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["e9cc47I/ZB9pBBlRc1mF4X"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 177}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 185}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["d0IDYyAklFG5m4bLhjQW9b"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 187}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["87Z7U/65RPg62ISLptGuY8"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 163}, "_prefab": {"__id__": 189}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 188}, "asset": {"__uuid__": "6a584c2c-5d3f-41b0-ae66-ac6fbfef7ebe", "__expectedType__": "cc.Prefab"}, "fileId": "d2Djdc8BdFaJQR2Fh3pjGd", "instance": {"__id__": 190}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "54BflX9dtAzK/WIhKJuUGl", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 191}, {"__id__": 193}, {"__id__": 194}, {"__id__": 195}, {"__id__": 196}, {"__id__": 198}, {"__id__": 200}, {"__id__": 202}, {"__id__": 204}, {"__id__": 206}, {"__id__": 207}, {"__id__": 209}, {"__id__": 210}, {"__id__": 212}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 192}, "propertyPath": ["_name"], "value": "downBoom"}, {"__type__": "cc.TargetInfo", "localID": ["d2Djdc8BdFaJQR2Fh3pjGd"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 192}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 80, "y": 60, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 192}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 192}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 197}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 100, "height": 100}}, {"__type__": "cc.TargetInfo", "localID": ["b74vQzPe1PvLyxwAcSQXup"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 199}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 120, "height": 41}}, {"__type__": "cc.TargetInfo", "localID": ["974/EebCxB2KRNFmWXkN/l"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 201}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 30, "height": 80}}, {"__type__": "cc.TargetInfo", "localID": ["d4RLfmhyhA1r/5jl+E+z1S"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 203}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -23, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["4eLfuzlNVFVp71AnntRWSk"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 205}, "propertyPath": ["title"], "value": "钻地手雷"}, {"__type__": "cc.TargetInfo", "localID": ["1ezry6dQhI3K85jLuFKluq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 192}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 208}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["e9cc47I/ZB9pBBlRc1mF4X"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 203}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 211}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["d0IDYyAklFG5m4bLhjQW9b"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 213}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["87Z7U/65RPg62ISLptGuY8"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 163}, "_prefab": {"__id__": 215}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 214}, "asset": {"__uuid__": "6a584c2c-5d3f-41b0-ae66-ac6fbfef7ebe", "__expectedType__": "cc.Prefab"}, "fileId": "d2Djdc8BdFaJQR2Fh3pjGd", "instance": {"__id__": 216}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "31nNMuA3xEVpsniSac68hp", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 217}, {"__id__": 219}, {"__id__": 220}, {"__id__": 221}, {"__id__": 222}, {"__id__": 224}, {"__id__": 226}, {"__id__": 228}, {"__id__": 230}, {"__id__": 232}, {"__id__": 233}, {"__id__": 235}, {"__id__": 236}, {"__id__": 238}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 218}, "propertyPath": ["_name"], "value": "Boom"}, {"__type__": "cc.TargetInfo", "localID": ["d2Djdc8BdFaJQR2Fh3pjGd"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 218}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 80, "y": -60, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 218}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 218}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 223}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 100, "height": 100}}, {"__type__": "cc.TargetInfo", "localID": ["b74vQzPe1PvLyxwAcSQXup"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 225}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 120, "height": 41}}, {"__type__": "cc.TargetInfo", "localID": ["974/EebCxB2KRNFmWXkN/l"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 227}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 80, "height": 80}}, {"__type__": "cc.TargetInfo", "localID": ["d4RLfmhyhA1r/5jl+E+z1S"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 229}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -23, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["4eLfuzlNVFVp71AnntRWSk"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 231}, "propertyPath": ["title"], "value": "地精炸弹"}, {"__type__": "cc.TargetInfo", "localID": ["1ezry6dQhI3K85jLuFKluq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 218}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 234}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["e9cc47I/ZB9pBBlRc1mF4X"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 229}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 237}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["d0IDYyAklFG5m4bLhjQW9b"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 239}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["87Z7U/65RPg62ISLptGuY8"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 163}, "_prefab": {"__id__": 241}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 240}, "asset": {"__uuid__": "6a584c2c-5d3f-41b0-ae66-ac6fbfef7ebe", "__expectedType__": "cc.Prefab"}, "fileId": "d2Djdc8BdFaJQR2Fh3pjGd", "instance": {"__id__": 242}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "7cHqoCei1IVYGtAcqANxyI", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 243}, {"__id__": 245}, {"__id__": 246}, {"__id__": 247}, {"__id__": 248}, {"__id__": 250}, {"__id__": 252}, {"__id__": 254}, {"__id__": 256}, {"__id__": 258}, {"__id__": 259}, {"__id__": 261}, {"__id__": 262}, {"__id__": 264}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 244}, "propertyPath": ["_name"], "value": "ladder"}, {"__type__": "cc.TargetInfo", "localID": ["d2Djdc8BdFaJQR2Fh3pjGd"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 244}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 80, "y": -180, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 244}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 244}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 249}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 100, "height": 100}}, {"__type__": "cc.TargetInfo", "localID": ["b74vQzPe1PvLyxwAcSQXup"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 251}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 120, "height": 41}}, {"__type__": "cc.TargetInfo", "localID": ["974/EebCxB2KRNFmWXkN/l"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 253}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 50, "height": 60}}, {"__type__": "cc.TargetInfo", "localID": ["d4RLfmhyhA1r/5jl+E+z1S"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 255}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -23, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["4eLfuzlNVFVp71AnntRWSk"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 257}, "propertyPath": ["title"], "value": "梯子"}, {"__type__": "cc.TargetInfo", "localID": ["1ezry6dQhI3K85jLuFKluq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 244}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 260}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["e9cc47I/ZB9pBBlRc1mF4X"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 255}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 263}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["d0IDYyAklFG5m4bLhjQW9b"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 265}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["87Z7U/65RPg62ISLptGuY8"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 163}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 480}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "405VUkW4xHsK6r0eu994nN"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 163}, "_enabled": true, "__prefab": null, "_resizeMode": 1, "_layoutType": 2, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 10, "_paddingRight": 10, "_paddingTop": 10, "_paddingBottom": 10, "_spacingX": 20, "_spacingY": 20, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 2, "_constraintNum": 1, "_affectedByScale": false, "_isAlign": false, "_id": "0fKqjhsLRBa7IwkiMxbJiP"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 163}, "_enabled": true, "__prefab": null, "_alignFlags": 36, "_target": null, "_left": 0, "_right": 160, "_top": 0, "_bottom": 85, "_horizontalCenter": 200, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "0anzLfciJJpJ8Zjo3ojShZ"}, {"__type__": "cc.Node", "_name": "Coin", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [{"__id__": 270}, {"__id__": 273}], "_active": true, "_components": [{"__id__": 276}, {"__id__": 277}, {"__id__": 278}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -298, "y": 438.143, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "11S2ovyOhFE6rgNRnr4aKv"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 269}, "_children": [], "_active": true, "_components": [{"__id__": 271}, {"__id__": 272}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "fcSMJ26CdCOLleOga71TYB"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 270}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d5MVFGHPpMnpGmhag9h7yg"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 270}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5a9041ee-b7b1-493e-8d70-91e9333f692f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "82yxOLU39BTrkVyQ9YrlBX"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 269}, "_children": [], "_active": true, "_components": [{"__id__": 274}, {"__id__": 275}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 30, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "abBQEwFwZDPLaoRTNO5fZJ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 273}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 23.889999389648438, "height": 41.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": "92FaVh8F9CLZRfMS6PgqRp"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 273}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "0", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a02df6db-4f52-4023-8bfb-6429098d02cf", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "82KUQKmEpOvqf8VOS44MBK"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 269}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "774s/UPulMd5MmP4RZlvEZ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 269}, "_enabled": true, "__prefab": null, "_alignFlags": 9, "_target": null, "_left": 12, "_right": 0, "_top": 171.85700000000003, "_bottom": 0, "_horizontalCenter": -300, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "78AF++zzZG4Yn2ngmd+Bwm"}, {"__type__": "b0a386NHWhNXaANc0EX/Etg", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 269}, "_enabled": true, "__prefab": null, "coinItem": {"__uuid__": "e6ddf297-a19f-4fb5-959d-37f36ad1751a", "__expectedType__": "cc.Prefab"}, "endCount": 0, "_id": "1enaU+wSBFe6Hvxjw4egux"}, {"__type__": "cc.Node", "_name": "Dirt", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [{"__id__": 280}, {"__id__": 283}], "_active": true, "_components": [{"__id__": 286}, {"__id__": 287}, {"__id__": 288}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -298, "y": 258.143, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a0ppGUP+RLpbNS1gUD2Las"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 279}, "_children": [], "_active": true, "_components": [{"__id__": 281}, {"__id__": 282}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e1qcggmGpGVYM/7M6oDf3Y"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 280}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 44, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "6d/ezetU1LCYEmvAkrWrVx"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 280}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "91fa3bf8-8d3d-428e-b673-53cc7a0006ab@e8b5d", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "9200KBqp1MDbnU1fYMyH6T"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 279}, "_children": [], "_active": true, "_components": [{"__id__": 284}, {"__id__": 285}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 30, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4acwH4J45KZ6AlCkef6/Cz"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 283}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 23.889999389648438, "height": 41.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": "52LGzXV0pDu7oHaL2soi+r"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 283}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "0", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a02df6db-4f52-4023-8bfb-6429098d02cf", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "20tRiK9HNB44GbvdRknI/N"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 279}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b4RewDEBtIkKxsm86mywTS"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 279}, "_enabled": true, "__prefab": null, "_alignFlags": 9, "_target": null, "_left": 12, "_right": 0, "_top": 351.857, "_bottom": 0, "_horizontalCenter": -300, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "b5DaSfHlxBgI5jBJBzqBpp"}, {"__type__": "b0a386NHWhNXaANc0EX/Etg", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 279}, "_enabled": true, "__prefab": null, "coinItem": {"__uuid__": "e6ddf297-a19f-4fb5-959d-37f36ad1751a", "__expectedType__": "cc.Prefab"}, "endCount": 0, "_id": "58oMv4pE9NvZjmTp2YjFIn"}, {"__type__": "cc.Node", "_name": "Diamond", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [{"__id__": 290}, {"__id__": 293}], "_active": true, "_components": [{"__id__": 296}, {"__id__": 297}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -298, "y": 378.143, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "04BCM0cr5Bxotj41ySwGL1"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 289}, "_children": [], "_active": true, "_components": [{"__id__": 291}, {"__id__": 292}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "edWv5MNlBJbJ5jI3CqLx4t"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 290}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 44, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "faKU24lr5P35ui6jfKrPEf"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 290}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d28b8e35-5d48-4497-8a60-9a82813dad72@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "95+7OQK69FSbZzI3oQZMN4"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 289}, "_children": [], "_active": true, "_components": [{"__id__": 294}, {"__id__": 295}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 30, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "82pazE+A5J65cve8lr2w+S"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 293}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 23.889999389648438, "height": 41.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": "69wp7RCk5Lo6MzSacCEPLM"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 293}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "0", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a02df6db-4f52-4023-8bfb-6429098d02cf", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "1b36dVyHdBGaGsoNi53CY6"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 289}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "51+yi6ma9IP7re1WnFlaEO"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 289}, "_enabled": true, "__prefab": null, "_alignFlags": 9, "_target": null, "_left": 12, "_right": 0, "_top": 231.85700000000003, "_bottom": 0, "_horizontalCenter": -300, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "9euQNCFvxP8ZP+ED/FSL4a"}, {"__type__": "cc.Node", "_name": "Keys", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [{"__id__": 299}, {"__id__": 302}], "_active": true, "_components": [{"__id__": 305}, {"__id__": 306}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -298, "y": 318.143, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "201LNmqIhLT5vfOYLTvK34"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 298}, "_children": [], "_active": true, "_components": [{"__id__": 300}, {"__id__": 301}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d3UfKcY+pN/5/IO7mcOAWS"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 299}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 44, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "477TCfXTNKyY8lV5UEBG1/"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 299}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "974469ff-18f5-4693-bdc1-5c7ebbdedaa2@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "1eu1a4mxtGkIzHGbElnpUg"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 298}, "_children": [], "_active": true, "_components": [{"__id__": 303}, {"__id__": 304}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 30, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "26IaMWiodOe75oAwNGZGRf"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 302}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 23.889999389648438, "height": 41.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": "39XnQBx6RJSrqwVR+OxzSa"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 302}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "0", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a02df6db-4f52-4023-8bfb-6429098d02cf", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "60cvG1i19Dq57klOLmM7Yw"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 298}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f0JLENs5xDv7Sj79Qojllz"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 298}, "_enabled": true, "__prefab": null, "_alignFlags": 9, "_target": null, "_left": 12, "_right": 0, "_top": 291.857, "_bottom": 0, "_horizontalCenter": -300, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "e3ZvD4dCJEiIWfnkj7aR/l"}, {"__type__": "cc.Node", "_name": "Bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [{"__id__": 308}, {"__id__": 317}, {"__id__": 327}], "_active": false, "_components": [{"__id__": 336}, {"__id__": 337}, {"__id__": 338}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9bOGcdTn5P6pTr6S/mWiwG"}, {"__type__": "cc.Node", "_name": "Title", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 307}, "_children": [{"__id__": 309}, {"__id__": 312}], "_active": true, "_components": [{"__id__": 315}, {"__id__": 316}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 290, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "31pj/woEhC8IEGbaIg7YIZ"}, {"__type__": "cc.Node", "_name": "Bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 308}, "_children": [], "_active": true, "_components": [{"__id__": 310}, {"__id__": 311}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "35y3V/ZeJKiJJdLp4wJAf3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 309}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 625, "height": 143}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "9fRA9EQ/VP66sdDRgSbxQW"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 309}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2a86b51a-ae20-4050-a2da-6599ae42d9e5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "69PsJkZ4JNaYogYn59nqP+"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 308}, "_children": [], "_active": true, "_components": [{"__id__": 313}, {"__id__": 314}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 19.615, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "24xckD/q1CmpsfinGgAAb+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 312}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 291.43988037109375, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a9BZJVbt9GE7FvGGb4p48b"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 312}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Congratulations", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a02df6db-4f52-4023-8bfb-6429098d02cf", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "66GP8dtRtEa42ZfTt33z4j"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 308}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "44oBKhm+pMzJ56E+ifNVwF"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 308}, "_enabled": true, "__prefab": null, "_alignFlags": 17, "_target": null, "_left": 0, "_right": 0, "_top": 300, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "228PwcocJL3qiUIXUx67dw"}, {"__type__": "cc.Node", "_name": "Image", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 307}, "_children": [{"__id__": 318}, {"__id__": 322}], "_active": true, "_components": [{"__id__": 325}, {"__id__": 326}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 50, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b99pInbAVLQI8e23QMswdz"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 317}, "_children": [], "_active": true, "_components": [{"__id__": 319}, {"__id__": 320}, {"__id__": 321}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f50HbudVpLrayARyc31oGI"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 318}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 800, "height": 800}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b7speJLz1Ge5Po+RYdyqh3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 318}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e71da7cc-78bc-4bd1-b6be-c80802625911@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "03PR3tTMFHhaA93sx73R6x"}, {"__type__": "3d99e8It6VBcKxstOZ4JQno", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 318}, "_enabled": true, "__prefab": null, "_id": "35c1E+WYFLdbI7gurykkdR"}, {"__type__": "cc.Node", "_name": "Sprite1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 317}, "_children": [], "_active": true, "_components": [{"__id__": 323}, {"__id__": 324}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "baHKRw1A9F2rirX3tdupsV"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 322}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 679, "height": 388}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d7dMHzHjBIiafMRtJb8CLJ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 322}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b1d9bb65-7686-46f1-904b-0ea14514b29d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "18BvBda3dLcYJwXrNXCH2L"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 317}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d0a8WfIspGTb3BX6t3vT6D"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 317}, "_enabled": true, "__prefab": null, "_alignFlags": 18, "_target": null, "_left": 0, "_right": 0, "_top": 545.548, "_bottom": 634.452, "_horizontalCenter": 0, "_verticalCenter": 50, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": "45oX/m4EhJGJD88tp+ZafG"}, {"__type__": "cc.Node", "_name": "Content", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 307}, "_children": [{"__id__": 328}, {"__id__": 331}], "_active": true, "_components": [{"__id__": 334}, {"__id__": 335}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 50, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "dc8kMznjBGUbtSRNI1earW"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 327}, "_children": [], "_active": true, "_components": [{"__id__": 329}, {"__id__": 330}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "99sUNn9CJOGJwX3l1zaNaN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 328}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 34}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "4aLkHa7N9Kr6mh610k3OOj"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 328}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5a9041ee-b7b1-493e-8d70-91e9333f692f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "cayQlvuLFM1pZQjOIwnvud"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 327}, "_children": [], "_active": true, "_components": [{"__id__": 332}, {"__id__": 333}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -217.714, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "cbfmG6H/tANbh0NYuy2kJY"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 331}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 209.3098907470703, "height": 90.39999999999999}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b9n5/Kz4pMRb1ven8cudoM"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 331}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Rewards   +100\nDiscovery +10", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a02df6db-4f52-4023-8bfb-6429098d02cf", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "03lJEX691GAIYMOTi3rSz1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 327}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "94dxtqgfNMfa/EmmlY9hcZ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 327}, "_enabled": true, "__prefab": null, "_alignFlags": 18, "_target": null, "_left": 310, "_right": 310, "_top": 590, "_bottom": 590, "_horizontalCenter": 0, "_verticalCenter": 50, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": "45fY93/o1Kq581RF5g9zQU"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 307}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f1yT+R6HVNjZ7OVmRliWwq"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 307}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 220}, "_spriteFrame": {"__uuid__": "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "3fw0WzWlZAQK09oO0pSDlh"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 307}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 720, "_originalHeight": 1280, "_alignMode": 2, "_lockFlags": 0, "_id": "ddRR74RAlGuKTTm48RBCti"}, {"__type__": "cc.Node", "_name": "RewardsExpProgress", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [{"__id__": 340}, {"__id__": 343}], "_active": true, "_components": [{"__id__": 346}, {"__id__": 347}, {"__id__": 348}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -150, "y": -236.703, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "98oFmG09ZOpYCIH595SARH"}, {"__type__": "cc.Node", "_name": "Bar", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 339}, "_children": [], "_active": true, "_components": [{"__id__": 341}, {"__id__": 342}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 300, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "aaxEXE5QpHg6F1ecmSr5sh"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 340}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_id": "bauxChNk9AmL2oTmjjdtet"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 340}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_spriteFrame": {"__uuid__": "24a704da-2867-446d-8d1a-5e920c75e09d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 1, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "2enpLYJ7dL4pv9+0WwxS/3"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 339}, "_children": [], "_active": true, "_components": [{"__id__": 344}, {"__id__": 345}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 150, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a6NnfBkitD3Lv5rMnkm0f+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 343}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 30.279953002929688, "height": 25.2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d9eMXHxV9COLVlPToW0sfO"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 343}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "LV1", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 20, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a02df6db-4f52-4023-8bfb-6429098d02cf", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "80lfO+pVtKuJE0xhgLf1qE"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 339}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": "bcCzEeieVIU5EnYRu4hs0Y"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 339}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 24, "g": 178, "b": 114, "a": 255}, "_spriteFrame": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "850rLUUpFNA4lj9zldCrIC"}, {"__type__": "cc.ProgressBar", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 339}, "_enabled": true, "__prefab": null, "_barSprite": {"__id__": 342}, "_mode": 0, "_totalLength": 300, "_progress": 1, "_reverse": true, "_id": "9aJ7gHfv9HNLXy+BcoHndf"}, {"__type__": "cc.Node", "_name": "Btn", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [{"__id__": 350}, {"__id__": 362}], "_active": false, "_components": [{"__id__": 368}, {"__id__": 369}, {"__id__": 370}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -350, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "73mGx//OFPn6QItWe4qV/L"}, {"__type__": "cc.Node", "_name": "AD", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 349}, "_children": [{"__id__": 351}], "_active": true, "_components": [{"__id__": 360}, {"__id__": 361}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 41, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f1tGj9DQ1GKq38Xup2418J"}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 350}, "_children": [{"__id__": 352}, {"__id__": 355}], "_active": true, "_components": [{"__id__": 358}, {"__id__": 359}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f9hdpdNBBCBLgmLvSfUHDK"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 351}, "_children": [], "_active": true, "_components": [{"__id__": 353}, {"__id__": 354}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -54.06498718261719, "y": 4.844, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "dcNCATuSZA6r6gGWxInwzL"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 352}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "38QbVn0rhLwpvhRCaXfFg7"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 352}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0fc820f9-02cc-40b4-9220-dd906e7dfb58@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "b5Azv07DVPGpb1xlybo2Xk"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 351}, "_children": [], "_active": true, "_components": [{"__id__": 356}, {"__id__": 357}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 22.5, "y": 4.148, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "dacXp2zhlOeq4BAEWO3g3T"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 355}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 98.12997436523438, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c15Qg8NIpNE4u5/vdL4Dvr"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 355}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Double", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a02df6db-4f52-4023-8bfb-6429098d02cf", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "233E/aXUxJOoHPnaszjDGe"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 351}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 163.12997436523438, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "9cFle6sx5MXbNshKVm3dxU"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 351}, "_enabled": true, "__prefab": null, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 10, "_paddingRight": 10, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 10, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": "aaXzY8E+1DrqELmzEf5Qbl"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 350}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 260, "height": 82}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "05A1vsQaxBkaZR/zkQj1Ms"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 350}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "49afdc73-06f8-4f64-bedf-6461ce5d381d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "51X3tdzvZMW6byIq3XOKOG"}, {"__type__": "cc.Node", "_name": "Back", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 349}, "_children": [{"__id__": 363}], "_active": true, "_components": [{"__id__": 366}, {"__id__": 367}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -41, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "323TxNHG9N+q2/MXTfxuyG"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 362}, "_children": [], "_active": true, "_components": [{"__id__": 364}, {"__id__": 365}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 4.973, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f5mymdRrpCtr1x+W8GsTu/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 363}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e8s3YdIgJCJJ8K25Nn8zN5"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 363}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "返回城镇", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "5aGfa77ghOFqNtO6dq1Xz8"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 362}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 260, "height": 82}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "2eaNfaHXZK3ITlXN2Gpwko"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 362}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "49afdc73-06f8-4f64-bedf-6461ce5d381d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "3fDJSpKRhMoqxi6C6xlkWK"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 349}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 520, "height": 164}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "7bi5f70SxAmbdTG+8Ia3f0"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 349}, "_enabled": true, "__prefab": null, "_alignFlags": 18, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": -350, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "02Hc2tAdBOgKG8t6BjMer5"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 349}, "_enabled": true, "__prefab": null, "_resizeMode": 1, "_layoutType": 2, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 10, "_paddingRight": 10, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 40, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": "42coxGW1lLOI1HQsnFlAdE"}, {"__type__": "cc.Node", "_name": "LevelUpPopup", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [{"__id__": 372}, {"__id__": 376}, {"__id__": 385}, {"__id__": 395}, {"__id__": 401}], "_active": false, "_components": [{"__id__": 407}, {"__id__": 408}, {"__id__": 409}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e8el87aBRNtqWL+Cs8YUNb"}, {"__type__": "cc.Node", "_name": "SpriteSplash", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 371}, "_children": [], "_active": true, "_components": [{"__id__": 373}, {"__id__": 374}, {"__id__": 375}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "00i03LZ2lFB4nTl7seYb8W"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 372}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f6ICddKyNJOqmkG3k5At4d"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 372}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_spriteFrame": {"__uuid__": "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "34qhKKJ4hJw4XO339RvYET"}, {"__type__": "3efa5fvvVlDGav8zaKCPqoc", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 372}, "_enabled": true, "__prefab": null, "_id": "3enbBFDLlEa4ICvXYn4K9J"}, {"__type__": "cc.Node", "_name": "Title", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 371}, "_children": [{"__id__": 377}, {"__id__": 380}], "_active": true, "_components": [{"__id__": 383}, {"__id__": 384}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 290, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "63shZsvUtEuaAJP5YWmDy2"}, {"__type__": "cc.Node", "_name": "Bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 376}, "_children": [], "_active": true, "_components": [{"__id__": 378}, {"__id__": 379}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c351W/Nj1K4JZ4zgtV842N"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 377}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 625, "height": 143}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e4BhNXKKBCo6A3dZ/H2Pkj"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 377}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2a86b51a-ae20-4050-a2da-6599ae42d9e5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "e1cZ+bFAJCAbi1Wfpcvfqw"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 376}, "_children": [], "_active": true, "_components": [{"__id__": 381}, {"__id__": 382}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 19.615, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "detFqfeGxKsq3VjcnbXjwU"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 380}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 153.3999481201172, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "27vlgQEyZAVJiAJ367AZHK"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 380}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Upgrade", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a02df6db-4f52-4023-8bfb-6429098d02cf", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "ebmzDKLolMK67y8OCmllec"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 376}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "6eTtkP/IBGl5vbNi/iyQZS"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 376}, "_enabled": true, "__prefab": null, "_alignFlags": 17, "_target": null, "_left": 0, "_right": 0, "_top": 300, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "denHV6MAZEZYGxlT2qJ9gO"}, {"__type__": "cc.Node", "_name": "Image", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 371}, "_children": [{"__id__": 386}, {"__id__": 390}], "_active": true, "_components": [{"__id__": 393}, {"__id__": 394}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 50, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "74ymOPljhK1Yp6RtP8UvpG"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 385}, "_children": [], "_active": true, "_components": [{"__id__": 387}, {"__id__": 388}, {"__id__": 389}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d19FM7V3tLRIjt01s87oeR"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 386}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 800, "height": 800}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "4ba0iYsXRHqrVSQOQZoqg3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 386}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e71da7cc-78bc-4bd1-b6be-c80802625911@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "56QNnyMD9DJL4AFr5Hw505"}, {"__type__": "3d99e8It6VBcKxstOZ4JQno", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 386}, "_enabled": true, "__prefab": null, "_id": "f7dUZMqbdB+JYLKEKFPIiX"}, {"__type__": "cc.Node", "_name": "Sprite1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 385}, "_children": [], "_active": true, "_components": [{"__id__": 391}, {"__id__": 392}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "313Ghu/9JEMIUHYJoB/4wV"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 390}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 679, "height": 388}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "eczIAW4AFL9KywsxqD14q+"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 390}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b1d9bb65-7686-46f1-904b-0ea14514b29d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "25mppjl+tE65Ky4S5dud8t"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 385}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "feld+AbFRNyrnbpslZtDKE"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 385}, "_enabled": true, "__prefab": null, "_alignFlags": 18, "_target": null, "_left": 0, "_right": 0, "_top": 545.548, "_bottom": 634.452, "_horizontalCenter": 0, "_verticalCenter": 50, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": "6fVhMoxupNjZDyFTO+s0s9"}, {"__type__": "cc.Node", "_name": "Content", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 371}, "_children": [{"__id__": 396}], "_active": true, "_components": [{"__id__": 399}, {"__id__": 400}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 50, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2eaD+Ri/RISq9A5et7FPmE"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 395}, "_children": [], "_active": true, "_components": [{"__id__": 397}, {"__id__": 398}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "6cST1sWeVM4r/0WoeOjPnM"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 396}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 149.99996948242188, "height": 126}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "6beXQn2eRBNY/EYC/BtHeJ"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 396}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Lv3", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 100, "_fontSize": 100, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 100, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a02df6db-4f52-4023-8bfb-6429098d02cf", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "56Jywa0B1BW7QjEHo/sdF1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 395}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "ebNmzq0uFJEKmJ1UnLMLXO"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 395}, "_enabled": true, "__prefab": null, "_alignFlags": 18, "_target": null, "_left": 310, "_right": 310, "_top": 590, "_bottom": 590, "_horizontalCenter": 0, "_verticalCenter": 50, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": "6apG7QZ8FNcZxdrnPdaJER"}, {"__type__": "cc.Node", "_name": "Btn", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 371}, "_children": [{"__id__": 402}], "_active": false, "_components": [{"__id__": 405}, {"__id__": 406}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -300, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a6YDaQf95PxZCQQrjahFBk"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 401}, "_children": [], "_active": true, "_components": [{"__id__": 403}, {"__id__": 404}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 4, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "bd3qkOBC9GwpBqjX409m6O"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 402}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 71.03993225097656, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "3dTJ1jKlRJSp0Rleh3tedh"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 402}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Get It", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a02df6db-4f52-4023-8bfb-6429098d02cf", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "7fE3IHDCJO07c8moQ/Plpz"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 401}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 260, "height": 82}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "9crb+2thhCwL1CrfzcDtz1"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 401}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "49afdc73-06f8-4f64-bedf-6461ce5d381d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "c5v/y18IJGjJiFa6Z/8W8E"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 371}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1fW2EwUYFN4J0xNJziXGvP"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 371}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 220}, "_spriteFrame": {"__uuid__": "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "7cm+ehbH5LrrKVX04AsCiG"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 371}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 720, "_originalHeight": 1280, "_alignMode": 2, "_lockFlags": 0, "_id": "45TUqGhL9Ba6CUYBEYHRoJ"}, {"__type__": "cc.Node", "_name": "warehouse", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [], "_active": false, "_components": [{"__id__": 411}, {"__id__": 412}, {"__id__": 413}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -220, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "27Iz22K6hM9qdLO/tMGPCq"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 410}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 128, "height": 128}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "ceKRPK+FBMO7z+WA5ZBwT9"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 410}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "91fa3bf8-8d3d-428e-b673-53cc7a0006ab@e2937", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "3aZ6OcjFpEVJUnIPmS1UcO"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 410}, "_enabled": true, "__prefab": null, "_alignFlags": 18, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": -220, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "97ugcqJD9CJqep49fatUUm"}, {"__type__": "cc.Node", "_name": "CardsPopup", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [{"__id__": 415}, {"__id__": 419}, {"__id__": 421}], "_active": false, "_components": [{"__id__": 423}, {"__id__": 424}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "83bS1yyaBARKkh4oZtlgA2"}, {"__type__": "cc.Node", "_name": "Title", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 414}, "_children": [], "_active": true, "_components": [{"__id__": 416}, {"__id__": 417}, {"__id__": 418}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 251.79999999999998, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "55JDLqa49KaYGAboNrdHa+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 415}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 352.87786865234375, "height": 58.92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "ff6q9rl7FJPIDRSkC6nOpE"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 415}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Upgrade your skill", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 42, "_fontSize": 42, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 42, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a02df6db-4f52-4023-8bfb-6429098d02cf", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "f9kPCd6TZAlqq3pCrvu8ox"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 415}, "_enabled": true, "__prefab": null, "_alignFlags": 17, "_target": {"__id__": 2}, "_left": 0, "_right": 0, "_top": 358.74, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "c9wLWyGyFAP5EgOeyssWLS"}, {"__type__": "cc.Node", "_name": "CardsContainer", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 414}, "_children": [], "_active": true, "_components": [{"__id__": 420}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e39ZU5xM1LbILzEmaHypjC"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 419}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "28aPw8EHFKy73RBJeCW1Up"}, {"__type__": "cc.Node", "_name": "BtnsContainer", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 414}, "_children": [], "_active": true, "_components": [{"__id__": 422}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "605wkSHOZM9rHQe4D3ttsq"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 421}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "45FUDys3VASLCqwAYZOris"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 414}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c3V/Cep99O1q0Xe3k61xju"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 414}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_miterLimit": 10, "_id": "75u+tce7xA6K0bVStmqeQo"}, {"__type__": "cc.Node", "_name": "FeverDeadLine", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [], "_active": true, "_components": [{"__id__": 426}, {"__id__": 427}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8fxrpi+ChKg4ZsIPVPJjei"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 425}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 594.2811240999379, "height": 138.6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "4aqC8IUnxNMLS9Eyeybwjp"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 425}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Fever Mode", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 110, "_fontSize": 110, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 110, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a02df6db-4f52-4023-8bfb-6429098d02cf", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": true, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "54o6czSzhLForzGWWqyRz7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 20}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "87jLvNpMJGnrsdkgF9QUDR"}, {"__type__": "3a24fRdS2hOJ7uhGZ64ACll", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 20}, "_enabled": true, "__prefab": null, "heartFab": {"__uuid__": "04bac067-83a4-440e-8a72-eb7ff71bef60", "__expectedType__": "cc.Prefab"}, "backpackItem": {"__uuid__": "e6ab2e12-5c21-479a-8cd6-63c5e55fa62a", "__expectedType__": "cc.Prefab"}, "oneCardPrefab": {"__uuid__": "b52e23ff-c1b5-442e-a037-b33a55f3b641", "__expectedType__": "cc.Prefab"}, "upBoomIcon": {"__uuid__": "0ce747ea-f57b-467a-9600-3a2ecf3b4ba2@058c8", "__expectedType__": "cc.SpriteFrame"}, "downBoomIcon": {"__uuid__": "7c8b3032-63ae-4385-b5a7-a7bca7809a95@83738", "__expectedType__": "cc.SpriteFrame"}, "BoomIcon": {"__uuid__": "9b0e48e7-4f33-400a-b748-4c5ea81925c8@8e16b", "__expectedType__": "cc.SpriteFrame"}, "LadderIcon": {"__uuid__": "91fa3bf8-8d3d-428e-b673-53cc7a0006ab@37b53", "__expectedType__": "cc.SpriteFrame"}, "LevelS": {"__id__": 122}, "playerFrame": {"__uuid__": "1c504167-573d-454a-8c9f-002cff4c69fd@9d186", "__expectedType__": "cc.SpriteFrame"}, "AlertPrefab": {"__uuid__": "da31597c-6f90-40fe-a9d4-a747c5bc70b7", "__expectedType__": "cc.Prefab"}, "BackBtnNode": {"__id__": 362}, "AdNode": {"__id__": 350}, "missionFailedTitleBg": {"__uuid__": "53f8359e-9362-4c6b-bc79-1b373df04266@f9941", "__expectedType__": "cc.SpriteFrame"}, "congratulationsTitleBg": {"__uuid__": "2a86b51a-ae20-4050-a2da-6599ae42d9e5@f9941", "__expectedType__": "cc.SpriteFrame"}, "gameTargetTempPrefab": {"__uuid__": "ad845568-bb99-4eff-b0b7-a5a3d442ad86", "__expectedType__": "cc.Prefab"}, "_id": "1eXJBch7ZHbZeKiUejtFWV"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 20}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 720, "_originalHeight": 1280, "_alignMode": 2, "_lockFlags": 0, "_id": "b0kFXVX8NNhp0p2nVx6m8R"}, {"__type__": "cc.Node", "_name": "Item-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 19}, "_children": [{"__id__": 432}, {"__id__": 448}, {"__id__": 464}], "_active": true, "_components": [{"__id__": 480}, {"__id__": 481}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -55, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c301zSBopHlKdBJrnmr8io"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 431}, "_prefab": {"__id__": 433}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 432}, "asset": {"__uuid__": "ebba3783-235e-4cbb-9b86-508d1e83fa9b", "__expectedType__": "cc.Prefab"}, "fileId": "70nnnJJtdDZ6rdoQwqzAfq", "instance": {"__id__": 434}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "35VZvjtXpFNIsOJygW9Uux", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 435}, {"__id__": 437}, {"__id__": 438}, {"__id__": 439}, {"__id__": 440}, {"__id__": 441}, {"__id__": 443}, {"__id__": 444}, {"__id__": 446}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 436}, "propertyPath": ["_name"], "value": "BackpackItem"}, {"__type__": "cc.TargetInfo", "localID": ["70nnnJJtdDZ6rdoQwqzAfq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 436}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -90, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 436}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 436}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 436}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 442}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 80, "height": 80}}, {"__type__": "cc.TargetInfo", "localID": ["62cZDZRcNAJrqHanui5lAr"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 436}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 445}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["2fE7vLHidCDpCpem7fCzxL"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 447}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["67L+86sPNGnJjBtwiWqKbV"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 431}, "_prefab": {"__id__": 449}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 448}, "asset": {"__uuid__": "ebba3783-235e-4cbb-9b86-508d1e83fa9b", "__expectedType__": "cc.Prefab"}, "fileId": "70nnnJJtdDZ6rdoQwqzAfq", "instance": {"__id__": 450}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "0bVdvuqqtPx6MSPvAL4Jjm", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 451}, {"__id__": 453}, {"__id__": 454}, {"__id__": 455}, {"__id__": 456}, {"__id__": 457}, {"__id__": 459}, {"__id__": 460}, {"__id__": 462}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 452}, "propertyPath": ["_name"], "value": "BackpackItem"}, {"__type__": "cc.TargetInfo", "localID": ["70nnnJJtdDZ6rdoQwqzAfq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 452}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -45, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 452}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 452}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 452}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 458}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 80, "height": 80}}, {"__type__": "cc.TargetInfo", "localID": ["62cZDZRcNAJrqHanui5lAr"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 452}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 461}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["2fE7vLHidCDpCpem7fCzxL"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 463}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["67L+86sPNGnJjBtwiWqKbV"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 431}, "_prefab": {"__id__": 465}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 464}, "asset": {"__uuid__": "ebba3783-235e-4cbb-9b86-508d1e83fa9b", "__expectedType__": "cc.Prefab"}, "fileId": "70nnnJJtdDZ6rdoQwqzAfq", "instance": {"__id__": 466}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "63a0gAvv1B3K8a5psY17Oc", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 467}, {"__id__": 469}, {"__id__": 470}, {"__id__": 471}, {"__id__": 472}, {"__id__": 473}, {"__id__": 475}, {"__id__": 476}, {"__id__": 478}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 468}, "propertyPath": ["_name"], "value": "BackpackItem"}, {"__type__": "cc.TargetInfo", "localID": ["70nnnJJtdDZ6rdoQwqzAfq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 468}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 468}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 468}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 468}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 474}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 80, "height": 80}}, {"__type__": "cc.TargetInfo", "localID": ["62cZDZRcNAJrqHanui5lAr"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 468}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 477}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["2fE7vLHidCDpCpem7fCzxL"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 479}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["67L+86sPNGnJjBtwiWqKbV"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 431}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 10, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "7clyVyHmlPlaELMRXLaiIV"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 431}, "_enabled": true, "__prefab": null, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 10, "_paddingRight": 10, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 10, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": "3dPrT6cx5B1oKZdlwj6wKL"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 560, "height": 210}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "42ZvPoPxlMe6FFuD0U4LHr"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": null, "_alignFlags": 17, "_target": null, "_left": 0, "_right": 0, "_top": 421.62, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 440, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "31oqtpEqVFt4egMLsTofLZ"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 18}, "_prefab": {"__id__": 485}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 484}, "asset": {"__uuid__": "ebba3783-235e-4cbb-9b86-508d1e83fa9b", "__expectedType__": "cc.Prefab"}, "fileId": "70nnnJJtdDZ6rdoQwqzAfq", "instance": {"__id__": 486}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "08uWI/C7FJcJZI8bc3iDf8", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 487}, {"__id__": 489}, {"__id__": 490}, {"__id__": 491}, {"__id__": 492}, {"__id__": 494}, {"__id__": 495}, {"__id__": 497}, {"__id__": 498}, {"__id__": 500}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 488}, "propertyPath": ["_name"], "value": "BackpackItem"}, {"__type__": "cc.TargetInfo", "localID": ["70nnnJJtdDZ6rdoQwqzAfq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 488}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 488}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 488}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 493}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 72.3642578125, "height": 41.8}}, {"__type__": "cc.TargetInfo", "localID": ["94d4tlUX9L6Joo6P1wO3jv"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 488}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 496}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 80, "height": 80}}, {"__type__": "cc.TargetInfo", "localID": ["62cZDZRcNAJrqHanui5lAr"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 488}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 499}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["2fE7vLHidCDpCpem7fCzxL"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 501}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["67L+86sPNGnJjBtwiWqKbV"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 18}, "_prefab": {"__id__": 503}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 502}, "asset": {"__uuid__": "ebba3783-235e-4cbb-9b86-508d1e83fa9b", "__expectedType__": "cc.Prefab"}, "fileId": "70nnnJJtdDZ6rdoQwqzAfq", "instance": {"__id__": 504}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "bfZLBQJidNKaCwPhn4ErMU", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 505}, {"__id__": 507}, {"__id__": 508}, {"__id__": 509}, {"__id__": 510}, {"__id__": 511}, {"__id__": 513}, {"__id__": 514}, {"__id__": 516}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 506}, "propertyPath": ["_name"], "value": "BackpackItem"}, {"__type__": "cc.TargetInfo", "localID": ["70nnnJJtdDZ6rdoQwqzAfq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 506}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -90, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 506}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 506}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 506}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 512}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 80, "height": 80}}, {"__type__": "cc.TargetInfo", "localID": ["62cZDZRcNAJrqHanui5lAr"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 506}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 515}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["2fE7vLHidCDpCpem7fCzxL"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 517}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["67L+86sPNGnJjBtwiWqKbV"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 18}, "_prefab": {"__id__": 519}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 518}, "asset": {"__uuid__": "ebba3783-235e-4cbb-9b86-508d1e83fa9b", "__expectedType__": "cc.Prefab"}, "fileId": "70nnnJJtdDZ6rdoQwqzAfq", "instance": {"__id__": 520}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "26KFR7ILdJGIBXySyzsMVI", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 521}, {"__id__": 523}, {"__id__": 524}, {"__id__": 525}, {"__id__": 526}, {"__id__": 527}, {"__id__": 529}, {"__id__": 530}, {"__id__": 532}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 522}, "propertyPath": ["_name"], "value": "BackpackItem"}, {"__type__": "cc.TargetInfo", "localID": ["70nnnJJtdDZ6rdoQwqzAfq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 522}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -45, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 522}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 522}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 522}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 528}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 80, "height": 80}}, {"__type__": "cc.TargetInfo", "localID": ["62cZDZRcNAJrqHanui5lAr"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 522}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 531}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["2fE7vLHidCDpCpem7fCzxL"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 533}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["67L+86sPNGnJjBtwiWqKbV"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 18}, "_prefab": {"__id__": 535}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 534}, "asset": {"__uuid__": "ebba3783-235e-4cbb-9b86-508d1e83fa9b", "__expectedType__": "cc.Prefab"}, "fileId": "70nnnJJtdDZ6rdoQwqzAfq", "instance": {"__id__": 536}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "70yadgBMpBQYSR8uUjhQ54", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 537}, {"__id__": 539}, {"__id__": 540}, {"__id__": 541}, {"__id__": 542}, {"__id__": 543}, {"__id__": 545}, {"__id__": 546}, {"__id__": 548}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 538}, "propertyPath": ["_name"], "value": "BackpackItem"}, {"__type__": "cc.TargetInfo", "localID": ["70nnnJJtdDZ6rdoQwqzAfq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 538}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 538}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 538}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 538}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 544}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 80, "height": 80}}, {"__type__": "cc.TargetInfo", "localID": ["62cZDZRcNAJrqHanui5lAr"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 538}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 547}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["2fE7vLHidCDpCpem7fCzxL"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 549}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["67L+86sPNGnJjBtwiWqKbV"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 10, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e1qIYrzqFE24yTwXupYqYG"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": null, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 10, "_paddingRight": 10, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 10, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": "28YbfbCSRFtZ7vvCWfYprI"}, {"__type__": "cc.Node", "_name": "Bg2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 553}, {"__id__": 554}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ed3sTId4pKkouhNkBRNeL/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 552}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1334, "height": 750}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "223ZiTNlBNArP5jTBPKn8H"}, {"__type__": "d59daH1hWpIZoj8SelaMI/s", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 552}, "_enabled": true, "__prefab": null, "layer0": {"__uuid__": "46ccd5b3-9c02-429d-b507-48891d63084d@f9941", "__expectedType__": "cc.SpriteFrame"}, "layer1": {"__uuid__": "5bc02f4e-3d33-4f3b-8d69-709c1c357728@f9941", "__expectedType__": "cc.SpriteFrame"}, "_id": "80gTxK8JhN07kiwLLdQC8/"}, {"__type__": "cc.Node", "_name": "TiledMap", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 556}, {"__id__": 559}], "_active": true, "_components": [{"__id__": 562}, {"__id__": 563}, {"__id__": 564}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 103.32200000000012, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1.2, "y": 1.2, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "5fmO2090BBdbZw+w+6xsvy"}, {"__type__": "cc.Node", "_name": "door_samples", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 555}, "_children": [], "_active": false, "_components": [{"__id__": 557}, {"__id__": 558}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "33n7J4y71BMLGDDKSpwegw"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 556}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 704, "height": 16448}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_id": "52jRbXLFBFhKOeifb+qIxn"}, {"__type__": "cc.T<PERSON><PERSON>ayer", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 556}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_id": "32g174p4BLcKaFwu/WLoFW"}, {"__type__": "cc.Node", "_name": "objects", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 555}, "_children": [], "_active": false, "_components": [{"__id__": 560}, {"__id__": 561}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 352, "y": -8224, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ecXl2kABxGQ6wBfvRciCtz"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 559}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 704, "height": 16448}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "61k8JuKUZH7aLZhqVn4OOT"}, {"__type__": "cc.TiledObjectGroup", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 559}, "_enabled": true, "__prefab": null, "_id": "8dbeWCHsRPSYG0QdKDNAN4"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 555}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 704, "height": 9728}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_id": "a9B8IIkINMI5LFwWF0V502"}, {"__type__": "759cci4BEFPNoaxb9aLfiPF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 555}, "_enabled": true, "__prefab": null, "playerFab": {"__uuid__": "67f46178-a21c-4553-926e-510ee06502f9", "__expectedType__": "cc.Prefab"}, "scarFab": {"__uuid__": "2558625c-17c1-4e7d-8b75-29c76460c38a", "__expectedType__": "cc.Prefab"}, "nextMinePortalFrame": {"__uuid__": "fca7783c-1757-40b3-93b6-e293771cc2d4@f9941", "__expectedType__": "cc.SpriteFrame"}, "playerBuffPrefab": {"__uuid__": "86bac04d-f061-479a-8767-c23afdd5bea4", "__expectedType__": "cc.Prefab"}, "mine01Fab": {"__uuid__": "927ad900-899e-442c-9122-8d77bd9a4cf4", "__expectedType__": "cc.Prefab"}, "trap01Fab": {"__uuid__": "db3429ef-d9b5-4b2b-bec4-da42d2b8bdee", "__expectedType__": "cc.Prefab"}, "bombFab": {"__uuid__": "392c28c1-58a1-4773-b350-cc537612c9c7", "__expectedType__": "cc.Prefab"}, "bombFlyingFab": {"__uuid__": "5dd79302-6470-4daa-8e14-72518f9ae113", "__expectedType__": "cc.Prefab"}, "bombGrenadeFab": {"__uuid__": "f9f19227-c013-4d46-9050-9c248b8bcfe5", "__expectedType__": "cc.Prefab"}, "rockFallFab": {"__uuid__": "14dc2cf9-f175-42bb-b214-116d7553c253", "__expectedType__": "cc.Prefab"}, "rockFall2Fab": {"__uuid__": "892f193c-d466-4ee2-8cb9-e8bd5b77503e", "__expectedType__": "cc.Prefab"}, "smokeFab": {"__uuid__": "59028983-85c5-4c25-a782-cb300790c7ef", "__expectedType__": "cc.Prefab"}, "glowingTileFab": {"__uuid__": "e22abfca-0c59-4bda-9b44-68d55c7436b5", "__expectedType__": "cc.Prefab"}, "oneCardFab": {"__uuid__": "b52e23ff-c1b5-442e-a037-b33a55f3b641", "__expectedType__": "cc.Prefab"}, "startBtnFab": {"__uuid__": "a7002569-9827-4928-aa75-6117951b4f25", "__expectedType__": "cc.Prefab"}, "dirtFrame": {"__uuid__": "91fa3bf8-8d3d-428e-b673-53cc7a0006ab@e8b5d", "__expectedType__": "cc.SpriteFrame"}, "adFrame": {"__uuid__": "9b73b7ef-d965-4a40-836d-c8756fabecc7@f9941", "__expectedType__": "cc.SpriteFrame"}, "mineHelpFab": {"__uuid__": "eccad1f9-**************-70a2190c31c1", "__expectedType__": "cc.Prefab"}, "sawFab": {"__uuid__": "e0fa2a2a-00c4-48e6-924b-bb3550a7db57", "__expectedType__": "cc.Prefab"}, "roundBladeFab": {"__uuid__": "03892478-bda0-40a6-b636-484667112d0e", "__expectedType__": "cc.Prefab"}, "twinBladeFab": {"__uuid__": "9f7620a4-ac27-4418-8403-a9031fb2e8e4", "__expectedType__": "cc.Prefab"}, "itemFloatUpPrefab": {"__uuid__": "9eb4ef7a-8cd5-47a9-b798-967e7033e635", "__expectedType__": "cc.Prefab"}, "playerHurtPrafab": {"__uuid__": "dc589047-4afa-45ae-bc95-6f3532e33163", "__expectedType__": "cc.Prefab"}, "mapBlockNodePrefab": {"__uuid__": "5c93c5e9-94ef-4c40-86cc-3ab0f7ac785b", "__expectedType__": "cc.Prefab"}, "hiddenRoomEntranceFab": {"__uuid__": "c69fd81c-065d-455f-8bdc-c3823ea365c4", "__expectedType__": "cc.Prefab"}, "_id": "2edcah+zFGioT1ZXit9cXR"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 555}, "_enabled": true, "__prefab": null, "_alignFlags": 17, "_target": null, "_left": 0, "_right": 0, "_top": 0.41927968749999994, "_bottom": 0, "_horizontalCenter": 422.4, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": false, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "b2Lq3ZG3FI4qMh73Wx+qnb"}, {"__type__": "cc.Node", "_name": "GameoverMask", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 566}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "49NDFZyQtC85gu5QATcbKf"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 565}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d4olxUaXpAQZ0+QqT+wgbQ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d6rUX5yfhMlKoWX2bSbawx"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_cameraComponent": {"__id__": 12}, "_alignCanvasWithScreen": true, "_id": "12O/ljcVlEqLmVm3U2gEOQ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 5.684341886080802e-14, "_bottom": 5.684341886080802e-14, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "c5V1EV8IpMtrIvY1OE9t2u"}, {"__type__": "cc.PrefabInfo", "root": null, "asset": null, "fileId": "25c62414-a7d4-47e6-b3d9-31f507797fc1", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 3}, {"__id__": 68}, {"__id__": 164}, {"__id__": 188}, {"__id__": 214}, {"__id__": 240}, {"__id__": 484}, {"__id__": 502}, {"__id__": 518}, {"__id__": 534}, {"__id__": 432}, {"__id__": 448}, {"__id__": 464}]}, {"__type__": "cc.SceneGlobals", "ambient": {"__id__": 572}, "shadows": {"__id__": 573}, "_skybox": {"__id__": 574}, "fog": {"__id__": 575}, "octree": {"__id__": 576}, "skin": {"__id__": 577}, "lightProbeInfo": {"__id__": 578}, "postSettings": {"__id__": 579}, "bakedWithStationaryMainLight": false, "bakedWithHighpLightmap": false}, {"__type__": "cc.AmbientInfo", "_skyColorHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyColor": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyIllumHDR": 20000, "_skyIllum": 20000, "_groundAlbedoHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_groundAlbedo": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_skyColorLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 1}, "_skyIllumLDR": 20000, "_groundAlbedoLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}}, {"__type__": "cc.ShadowsInfo", "_enabled": false, "_type": 0, "_normal": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_distance": 0, "_planeBias": 1, "_shadowColor": {"__type__": "cc.Color", "r": 76, "g": 76, "b": 76, "a": 255}, "_maxReceived": 4, "_size": {"__type__": "cc.Vec2", "x": 512, "y": 512}}, {"__type__": "cc.SkyboxInfo", "_envLightingType": 0, "_envmapHDR": null, "_envmap": null, "_envmapLDR": null, "_diffuseMapHDR": null, "_diffuseMapLDR": null, "_enabled": false, "_useHDR": true, "_editableMaterial": null, "_reflectionHDR": null, "_reflectionLDR": null, "_rotationAngle": 0}, {"__type__": "cc.FogInfo", "_type": 0, "_fogColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_enabled": false, "_fogDensity": 0.3, "_fogStart": 0.5, "_fogEnd": 300, "_fogAtten": 5, "_fogTop": 1.5, "_fogRange": 1.2, "_accurate": false}, {"__type__": "cc.OctreeInfo", "_enabled": false, "_minPos": {"__type__": "cc.Vec3", "x": -1024, "y": -1024, "z": -1024}, "_maxPos": {"__type__": "cc.Vec3", "x": 1024, "y": 1024, "z": 1024}, "_depth": 8}, {"__type__": "cc.SkinInfo", "_enabled": false, "_blurRadius": 0.01, "_sssIntensity": 3}, {"__type__": "cc.LightProbeInfo", "_giScale": 1, "_giSamples": 1024, "_bounces": 2, "_reduceRinging": 0, "_showProbe": true, "_showWireframe": true, "_showConvex": false, "_data": null, "_lightProbeSphereVolume": 1}, {"__type__": "cc.PostSettingsInfo", "_toneMappingType": 0}]