import { Vec3, Size } from 'cc';

/**
 * 地图工具类，提供坐标转换功能
 */
export class MapUtils {
    // 固定的tile大小，和地图中的tileSize保持一致
    public static readonly TILE_SIZE = new Size(64, 64);

    /**
     * 通过场景坐标获取对应的tile编号坐标
     * @param pos 场景坐标
     * @returns tile编号坐标，编号从左上开始，第一个是0
     */
    public static getGIDByLocation(pos: Vec3): Vec3 {
        let gid = new Vec3(
            Math.ceil(pos.x / this.TILE_SIZE.width) - 1,
            Math.ceil(Math.abs(pos.y) / this.TILE_SIZE.height) - 1,
            0
        );
        return gid;
    }

    /**
     * 通过tile编号坐标获取场景坐标(对应tile中心点)
     * @param gid tile编号坐标
     * @returns 场景坐标
     */
    public static getLocationByGID(gid: Vec3): Vec3 {
        let mapLocation = new Vec3(
            this.TILE_SIZE.width * (gid.x + 1) - this.TILE_SIZE.width / 2,
            -(this.TILE_SIZE.height * (gid.y + 1) - this.TILE_SIZE.height / 2),
            0
        );
        return mapLocation;
    }

    /**
     * 获取某个坐标中心点的场景坐标
     * @param x 原始x坐标
     * @param y 原始y坐标
     * @returns 场景坐标
     */
    public static getCenterLocation(x: number, y: number): Vec3 {
        const mapHeight = 152 * this.TILE_SIZE.height; // 根据GameConfig.mapMaxLevel计算
        return new Vec3(
            Math.ceil(x / this.TILE_SIZE.width) * this.TILE_SIZE.width - this.TILE_SIZE.width / 2,
            -(
                mapHeight -
                (Math.ceil(y / this.TILE_SIZE.height) * this.TILE_SIZE.height -
                    this.TILE_SIZE.height / 2)
            ),
            0
        );
    }

    /**
     * 根据位置和方向获取相邻的GID坐标
     * @param pos 场景坐标
     * @param dir 方向：'up', 'right', 'down', 'left'
     * @param mapSize 可选的地图尺寸，用于验证GID是否有效
     * @returns 相邻的GID坐标，如果超出地图范围则返回null
     */
    public static getNearGIDByLocation(pos: Vec3, dir: string, mapSize?: Size): Vec3 {
        let gid = this.getGIDByLocation(pos);
        let res = null;
        if (dir == 'up') {
            res = new Vec3(gid.x, gid.y - 1, 0);
        } else if (dir == 'right') {
            res = new Vec3(gid.x + 1, gid.y, 0);
        } else if (dir == 'down') {
            res = new Vec3(gid.x, gid.y + 1, 0);
        } else if (dir == 'left') {
            res = new Vec3(gid.x - 1, gid.y, 0);
        }

        // 如果提供了地图尺寸，验证GID是否有效
        if (mapSize && !this.isGIDValid(res, mapSize)) {
            return null;
        }

        return res;
    }

    /**
     * 检查GID坐标是否在地图范围内
     * @param gid GID坐标
     * @param mapSize 地图尺寸
     * @returns 是否有效
     */
    public static isGIDValid(gid: Vec3, mapSize: Size): boolean {
        if (
            gid.x < 0 ||
            gid.x > mapSize.width - 1 ||
            gid.y < 0 ||
            gid.y > mapSize.height - 1
        ) {
            return false;
        }
        return true;
    }
}
