import {
    _decorator,
    Animation,
    Color,
    Component,
    director,
    EventKeyboard,
    Input,
    input,
    instantiate,
    KeyCode,
    math,
    Node,
    Size,
    Sprite,
    tween,
    UITransform,
    Vec3
} from "cc";
import { EventBus } from "./EventBus";
import { GameConfig } from "./GameConfig";
import { GameData } from "./GameData";
import { Item } from "./Item";
import { Map } from "./Map";
import { HiddenMap } from "./HiddenRoom/HiddenMap";
import { Mine } from "./Mine";
import { Scar } from "./Scar";
import { UI } from "./UI";
import { UserData } from "./Utils/UserData";
import { Bomb } from "./Bomb";
import { saveUserData } from "./Utils/Tools";

import { PlayerHurtCtl } from "./Game/PlayerHurtCtl";
import { RigidBody2D } from "cc";
import { Vec2 } from "cc";

import { BackgroundVertical } from "./v1/Common/BackgroundVertical";
import { MapUtils } from './Utils/MapUtils';
const { ccclass } = _decorator;

@ccclass('Player')
export class Player extends Component {
    public commonAssets; // 装备卡图片资源

    public parts: Node;
    private body: Node;
    public map: Map | HiddenMap;

    private ui: UI;
    public weaponNode: Node;
    private shovelAnim: Animation;
    public bodyAnim: Animation;

    // private petNode: Node;

    // private states = {
    //     STAND: 1,
    //     MOVING: 2,
    // };

    // private state = this.states.STAND;

    // 获取用户战斗数据
    private playerBattle = this.getPlayerBattleInfo();
    // 血量
    public hpInitial = this.playerBattle.hp;

    public hp = this.hpInitial;
    private hpAutoDropDuration = 2; //s
    private hpAutoDropPassed = 0;

    public lampMinimalPower = 110; // 这个还会消耗，只是保留照明
    public lampInitialPower = 99999; // this.playerBattle.light;
    public lampPower = this.lampInitialPower;
    private lampRunSpeed = 20; // 15;
    public lampRunSpeedDelta = 0; // 增量，百分比

    public power = this.playerBattle.power;
    public powerDelta = 0; // 力量增量百分比

    private itemHandler: Item;
    public canBeDamaged = true; // 受伤冷却
    private beDamagedCooldown = 1.5; //s
    private beDamagedCollDownPassed = 0;
    private lastKiller = null; // 最后一次受伤的原因

    // 掉落伤害
    // private continueDropppedHeight = 0; // 当前连续掉落的高度，tile高度的倍数
    // private maxDropHeight = 999999; // 超过多高会造成伤害, 如果不想有掉落伤害把这个值调到最大, 0表示超过1格就有伤害
    // 超过maxDropHeight时开始有伤害，每超出一个tile高度伤害加一次
    // private dropPerHeightDamage = 1; //0.5;

    // 持续自动移动
    private isKeyPressing = false;
    public currentKeyPressDir = ''; // 最后按下的方向
    private autoMoveDelay = 0.3; // 自动移动和挖掘的间隔时间
    private autoMoveMaxDelay = 0.1; // 最快挖掘速度
    private autoMoveDelayPassed = 0;
    public autoMoveDelayDelta = 0;

    // 磁力吸附道具的半径
    public magnetRadius = 32; // 一格64
    public magnetRadiusDelta = 0;
    public currentExp = 0;
    public fullExp = 100;

    public dirtSpawnCoin = false;
    public digAreaHoriDelta = 0; // 横向增加几格攻击范围
    public digAreaVertDelta = 0; // 竖向增加几格攻击范围

    // 目前挖掘的土块数量，用于升级和本局买卡
    public currentDirt = 10; // 起始需要给一定的粘土
    public getDirtDelta = 0; // 挖掘粘土获得的增量，这个地方是倍数

    public bombDamageDelta = 0; // 炸弹造成的伤害增量，百分比
    private energyShieldNode: Node;
    public trapDamageDelta = 0; // 陷阱伤害，计算方式同上

    public explodeDigDamage = 0; // 挖掘是否会引发爆炸, 0表示没有起效，>0表示生效，而且代表攻击力

    public minesPriceRatio = 1; // 矿石价格倍率，默认1倍

    // 抽卡看广告全都要的次数，最多两次
    public drawCardGetAllTimes = 2;

    public isArriveTreasureNode: boolean = false; // 是否到达宝箱节点，用于判断player能否move

    // 物理
    private rigidBody: RigidBody2D;
    private worldCenter: Vec2 = new Vec2();
    private currentVelocity: Vec2 = new Vec2(0, 0);
    private maxDropSpeed = 500; // 最大下落速度

    private tmpLocation: Vec3 = new Vec3(0, 0, 0); // 临时位置
    private currentGID: Vec3 = new Vec3(0, 0, 0); // 当前所在的GID

    // 横向移动重构
    private horiMoving = false;
    private horiMoveStartX: number = 0; // 横向移动起始位置
    private horiMoveTargetDistance: number = 0; // 横向移动目标位置
    private horiMoveSpeed = 300;

    private bgScript: BackgroundVertical;

    start() {
        this.map = this.node.parent.getComponent(Map) as Map;
        if (!this.map) {
            this.map = this.node.parent.getComponent(HiddenMap) as HiddenMap;
        }
        this.commonAssets = this.map.commonAssets;
        this.parts = this.node.getChildByName('Parts');
        this.body = this.parts.getChildByName('Body');
        this.energyShieldNode = this.node.getChildByName('Shield');
        this.itemHandler = this.node.getComponent(Item) as Item;
        this.ui = director.getScene().getChildByPath('Canvas/UI').getComponent(UI) as UI;
        this.rigidBody = this.node.getComponent(RigidBody2D) as RigidBody2D;
        // 启用碰撞监听，确保能与宝箱等物体产生碰撞事件
        if (this.rigidBody) {
            this.rigidBody.enabledContactListener = true;
            console.log('Player: 已启用刚体碰撞监听, 组别:', this.rigidBody.group);
        }
        this.bgScript = director
            .getScene()
            .getChildByPath('Canvas/Bg2')
            .getComponent(BackgroundVertical);

        UserData.currentLevelDirtTotalCount = this.currentDirt; // 总共挖了多少块土块

        this.weaponNode = this.parts.getChildByName('Shovel');
        this.shovelAnim = this.weaponNode.getComponent(Animation);
        this.shovelAnim.createState(this.shovelAnim.clips[0], 'shovelup');
        this.shovelAnim.createState(this.shovelAnim.clips[1], 'shovelright');
        this.shovelAnim.createState(this.shovelAnim.clips[2], 'shoveldown');

        // 以宽度60为基础武器大小参考
        let baseWeaponWidth = GameConfig.weapons[UserData.currentWeapon].width;
        this.weaponNode.getComponent(Sprite).spriteFrame =
            this.commonAssets[`weapon${UserData.currentWeapon}Frame`];
        let weaponSize = this.weaponNode.getComponent(UITransform).contentSize;
        this.weaponNode
            .getComponent(UITransform)
            .setContentSize(
                new Size(baseWeaponWidth, baseWeaponWidth / (weaponSize.width / weaponSize.height))
            );

        this.bodyAnim = this.body.getComponent(Animation);
        this.bodyAnim.createState(this.bodyAnim.clips[0], 'idle');
        this.bodyAnim.createState(this.bodyAnim.clips[1], 'die');


        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        input.on(Input.EventType.KEY_UP, this.onKeyUp, this);

        EventBus.on('boomAction', this.boomAction, this);

        // 宠物
        // if (UserData.currentPetID !== -1) {
        //     let petID = UserData.currentPetID;
        //     this.petNode = instantiate(this.commonAssets[`pet${petID}Fab`]);
        //     this.petNode.getComponent(UITransform).priority = GameConfig.UIPriority.player;
        //     this.petNode.setSiblingIndex(GameConfig.UIPriority.player);
        //     this.map.node.addChild(this.petNode);
        // }

        if (GameData.mode === 'fever') {
            // 磁力吸附道具的半径
            this.magnetRadiusDelta = 2 * this.map.tileSize.width;
        }
    }

    boomAction(type: string) {
        if (type === 'boom') {
            this.itemHandler.plantBomb(GameConfig.blockKinds.BOMB_ZONE);
        }

        if (type === 'upBoom') {
            this.itemHandler.plantBomb(GameConfig.blockKinds.BOMB_FLYING);
        }

        if (type === 'downBoom') {
            this.itemHandler.plantBomb(GameConfig.blockKinds.BOMB_GRENADE);
        }

        if (type === 'ladder') {
            this.itemHandler.useLadder();
        }
    }

    onKeyDown(event: EventKeyboard) {
        if (this.cannotControl()) return;

        this.currentKeyPressDir = '';
        if (event.keyCode == KeyCode.ARROW_RIGHT || event.keyCode == KeyCode.KEY_D) {
            this.currentKeyPressDir = 'right';
        } else if (event.keyCode == KeyCode.ARROW_LEFT || event.keyCode == KeyCode.KEY_A) {
            this.currentKeyPressDir = 'left';
        }
        if (event.keyCode == KeyCode.ARROW_UP || event.keyCode == KeyCode.KEY_W) {
            this.currentKeyPressDir = 'up';
        } else if (event.keyCode == KeyCode.ARROW_DOWN || event.keyCode == KeyCode.KEY_S) {
            this.currentKeyPressDir = 'down';
        }
        this.startAutoMove();

        // 调试
        if (event.keyCode == KeyCode.KEY_Z) {
            // 区域炸弹（9宫格)
            this.itemHandler.plantBomb(GameConfig.blockKinds.BOMB_ZONE);
        } else if (event.keyCode == KeyCode.KEY_X) {
            // 向上炸弹 fire in the sky
            this.itemHandler.plantBomb(GameConfig.blockKinds.BOMB_FLYING);
        } else if (event.keyCode == KeyCode.KEY_C) {
            // 向下炸弹 fir in the hole
            this.itemHandler.plantBomb(GameConfig.blockKinds.BOMB_GRENADE);
        } else if (event.keyCode == KeyCode.KEY_V) {
            // 梯子
            this.itemHandler.useLadder();
        } else if (event.keyCode == KeyCode.KEY_J) {
            GameData._start_skip = true;
        } else {
            // 直接跳到指定层数，J + 数字 + 回车
            if (GameData._start_skip) {
                if (event.keyCode == KeyCode.DIGIT_0) GameData._skip_to_level_num += '0';
                if (event.keyCode == KeyCode.DIGIT_1) GameData._skip_to_level_num += '1';
                if (event.keyCode == KeyCode.DIGIT_2) GameData._skip_to_level_num += '2';
                if (event.keyCode == KeyCode.DIGIT_3) GameData._skip_to_level_num += '3';
                if (event.keyCode == KeyCode.DIGIT_4) GameData._skip_to_level_num += '4';
                if (event.keyCode == KeyCode.DIGIT_5) GameData._skip_to_level_num += '5';
                if (event.keyCode == KeyCode.DIGIT_6) GameData._skip_to_level_num += '6';
                if (event.keyCode == KeyCode.DIGIT_7) GameData._skip_to_level_num += '7';
                if (event.keyCode == KeyCode.DIGIT_8) GameData._skip_to_level_num += '8';
                if (event.keyCode == KeyCode.DIGIT_9) GameData._skip_to_level_num += '9';
                if (event.keyCode == KeyCode.ENTER) {
                    this.node.setPosition(
                        MapUtils.getLocationByGID(
                            new Vec3(5, parseInt(GameData._skip_to_level_num), 0)
                        )
                    );
                    GameData._start_skip = false;
                    GameData._skip_to_level_num = '';
                }
            }
        }

        // let playerGID = MapUtils.getGIDByLocation(this.node.position);
    }

    onKeyUp() {
        this.stopAutoMove();
    }

    public startAutoMove() {
        EventBus.emit('playSound', 'press');
        this.isKeyPressing = true;
    }

    public stopAutoMove() {
        this.isKeyPressing = false;
    }

    public cannotControl(): boolean {
        return (
            GameConfig.currentGameState == GameConfig.gameStates.PAUSED ||
            GameConfig.currentGameState == GameConfig.gameStates.GAMEOVER ||
            GameConfig.currentGameState == GameConfig.gameStates.SUCCESS ||
            GameConfig.currentGameState == GameConfig.gameStates.ENTERING_PORTAL
        );
    }

    arrowAction() {
        let dir = this.currentKeyPressDir;
        if (dir == 'up') {
            // 检查是否在隐藏房间入口位置
            if (this.map.isPlayerOnHiddenRoomEntrance(this.node.position)) {
                this.enterHiddenRoom();
                return;
            }
            this.digDir('up');
            // this.rigidBody.applyLinearImpulseToCenter(new Vec2(0, 30), true);
            // this.rigidBody.applyForceToCenter(new Vec2(0, 100), true);
        } else if (dir == 'right') {
            this.move('right');
        } else if (dir == 'down') {
            this.digDir('down');
        } else if (dir == 'left') {
            this.move('left');
        }
    }

    move(dir: string) {
        // if (this.state == this.states.MOVING) return;
        if (this.isArriveTreasureNode) return;

        if (dir == 'right') {
            // right checker
            let [tt] = this.map.getNearTileType(this.node.position, 'right', 'pos');

            if (tt == GameConfig.tileKinds.Dirt || tt == GameConfig.tileKinds.Obstacle) {
                this.parts.setScale(new Vec3(1, 1, 1));
                this.digDir('right');
                return;
            } else if (tt == GameConfig.tileKinds.Edge) {
                this.parts.setScale(new Vec3(1, 1, 1));
                return;
            } else if (tt == GameConfig.tileKinds.Exit) {
                this.enterExit(true, 2, 'right');
                return;
            }
            // this.state = this.states.MOVING;
            if (this.parts.scale.x < 0) {
                // 只转向
                tween(this.parts)
                    .to(
                        0.2,
                        {
                            scale: new Vec3(1, 1, 1),
                        },
                        {
                            onComplete: () => {
                                // this.state = this.states.STAND;
                            },
                        }
                    )
                    .start();
            } else {
                this.horiMoving = true;
                this.horiMoveStartX = this.node.position.x;
                this.horiMoveTargetDistance = this.map.tileSize.width;

            }
            this.map.spawnFeetSmokeAtGID(MapUtils.getGIDByLocation(this.node.position));
        } else if (dir == 'left') {
            // left checker
            let [tt] = this.map.getNearTileType(this.node.position, 'left', 'pos');
            if (tt == GameConfig.tileKinds.Dirt || tt == GameConfig.tileKinds.Obstacle) {
                this.parts.setScale(new Vec3(-1, 1, 1));
                this.digDir('left');
                return;
            } else if (tt == GameConfig.tileKinds.Edge) {
                this.parts.setScale(new Vec3(-1, 1, 1));
                return;
            } else if (tt == GameConfig.tileKinds.Exit) {
                this.enterExit(true, 2, 'left');
                return;
            }
            // this.state = this.states.MOVING;
            if (this.parts.scale.x > 0) {
                // 只转向
                tween(this.parts)
                    .to(
                        0.2,
                        {
                            scale: new Vec3(-1, 1, 1),
                        },
                        {
                            onComplete: () => {
                                // this.state = this.states.STAND;
                            },
                        }
                    )
                    .start();
            } else {
                this.horiMoving = true;
                this.horiMoveStartX = this.node.position.x;
                this.horiMoveTargetDistance = -this.map.tileSize.width;

            }
            this.map.spawnFeetSmokeAtGID(MapUtils.getGIDByLocation(this.node.position));
        }
    }

    fixLocationAfterMove() {
        this.tmpLocation = MapUtils.getGIDByLocation(this.node.position);
        this.tmpLocation = MapUtils.getLocationByGID(this.tmpLocation);
        this.node.setPosition(this.tmpLocation);
    }

    // 所有挖掘都会调用这个方法
    digDir(dir: string) {
        // if (this.state != this.states.STAND) return;
        if (MapUtils.getGIDByLocation(this.node.position).y > this.map.tileGIDMaxY + 2) return; // 最后几层不能挖了，否则会挖穿地图出bug
        let [tt, gid] = this.map.getNearTileType(this.node.position, dir, 'pos');

        // 检查是否是隐藏房间正下方的土块
        if (this.isHiddenRoomBottomTile(gid)) {
            this.playDigAnim(dir); // 只播放动画，不能挖掘
            return;
        }

        if (tt == GameConfig.tileKinds.Dirt || tt == GameConfig.tileKinds.Obstacle) {
            this.playDigAnim(dir);
            this.dig(gid, this.getRealPower());
        } else if (tt == GameConfig.tileKinds.Empty || tt == GameConfig.tileKinds.Edge) {
            this.playDigAnim(dir); // 只播放动画
        }

        // card，这里有两种卡片的效果，可以叠加
        // 横向或者竖向范围生效
        if (dir == 'up') {
            for (let i = 0; i < this.digAreaVertDelta; i++) {
                this.areaCheckGIDAndDig(new Vec3(gid.x, gid.y - 1 - i, 0));
            }
        } else if (dir == 'right') {
            for (let i = 0; i < this.digAreaHoriDelta; i++) {
                this.areaCheckGIDAndDig(new Vec3(gid.x + 1 + i, gid.y, 0));
            }
        } else if (dir == 'down') {
            for (let i = 0; i < this.digAreaVertDelta; i++) {
                this.areaCheckGIDAndDig(new Vec3(gid.x, gid.y + 1 + i, 0));
            }
        } else if (dir == 'left') {
            for (let i = 0; i < this.digAreaHoriDelta; i++) {
                this.areaCheckGIDAndDig(new Vec3(gid.x - 1 - i, gid.y, 0));
            }
        }

        // card, 挖掘产生爆炸效果
        if (this.explodeDigDamage > 0) this.digWithZoneBomb(gid);
    }

    // 检查是否是隐藏房间正下方的土块
    isHiddenRoomBottomTile(gid: Vec3): boolean {
        const currentChapter = GameData.currentLevelConfig;
        if (!currentChapter || !currentChapter.hiddenRooms) {
            return false;
        }

        const hiddenRooms = currentChapter.hiddenRooms;

        // hiddenRooms是一个对象，包含entranceGID属性
        if (hiddenRooms.entranceGID && Array.isArray(hiddenRooms.entranceGID)) {
            const [roomX, roomY] = hiddenRooms.entranceGID;

            // 直接用GID坐标比较，保护正下方一个土块
            if (gid.x === roomX && gid.y === roomY + 1) {
                console.log(`[HIDDEN_ROOM] 阻止挖掘隐藏房间下方土块，GID: (${gid.x}, ${gid.y}), 隐藏房间: (${roomX}, ${roomY})`);
                return true;
            }
        }

        return false;
    }

    areaCheckGIDAndDig(gid: Vec3) {
        let [tt, _] = this.map.getNearTileType(gid, 'no', 'gid');
        if (tt == GameConfig.tileKinds.Dirt || tt == GameConfig.tileKinds.Obstacle) {
            this.dig(gid, this.getRealPower());
        }
    }

    digWithZoneBomb(gid: Vec3) {
        let bomb = this.itemHandler.plantBomb(GameConfig.blockKinds.BOMB_ZONE);
        let pos = MapUtils.getLocationByGID(gid);
        bomb.setPosition(pos);
        bomb.getComponent(Bomb).level = 1;
        bomb.getComponent(Bomb).damage = this.explodeDigDamage;
    }

    playDigAnim(dir: string) {
        if (dir == 'up') {
            this.shovelAnim.play('shovelup');
        } else if (dir == 'right') {
            this.shovelAnim.play('shovelright');
        } else if (dir == 'down') {
            this.shovelAnim.play('shoveldown');
        } else if (dir == 'left') {
            this.shovelAnim.play('shovelright');
        }
    }

    // 总攻击力= 人物power(包括技能卡) + 武器power
    getRealPower(): number {
        return (
            (this.power + GameConfig.weapons[UserData.currentWeapon].power) * (1 + this.powerDelta)
        );
    }

    // 内部逻辑调用
    public dig(gid: Vec3, damage: number) {
        // this.continueDropppedHeight = 0;
        this.map.spawnSmokeAtGID(gid); // 挖掘时冒烟效果

        let [block, blockIdx] = this.map.getBlockAtGID(gid);
        if (blockIdx > -1) {
            block = block as Node;
            let mineS = block.getComponent(Mine) as Mine;
            if (block.name == GameConfig.blockKindNames.MINE) {
                EventBus.emit('playSound', 'dig');
                let isDead = mineS.beDamaged(damage); // 数组中删除由Map update执行
                if (isDead) {
                    // block.getComponent(Sprite).enabled = true; // 挖碎之后显示
                    // this.map.removeItemLayerTileAt(gid); // 删除地图上的ItemLayer的tile
                    // 代表了挖了东西，也可以用来统计挖了多少东西
                    this.currentDirt += 1 * (1 + this.getDirtDelta);
                    UserData.currentLevelDirtTotalCount += 1 * (1 + this.getDirtDelta);
                    // 挖掘时碎石效果
                    this.map.spawnRockFall2AtGID(gid);
                    // 保存地图数据, 矿石挖开就等于没有了,也就是挖出来后不捡就没有了
                    this.map.saveLevelMemo(`${gid.y}:${gid.x}`, { tile: -1, block: -1 });
                }
            } else if (block.name == GameConfig.blockKindNames.TRAP) {
                if (mineS.kind == GameConfig.blockKinds.TRAP_ROCK) {
                    let isDead = mineS.beDamaged(damage);
                    if (isDead) {
                        // 挖掘时碎石效果
                        this.map.spawnRockFall2AtGID(gid);
                        // 保存地图数据
                        this.map.saveLevelMemo(`${gid.y}:${gid.x}`, { tile: -1 });
                    }
                    EventBus.emit('playSound', 'hitRock');
                }
            }
        } else {
            //// 土
            // 可能已经没有土了
            let id = this.map.existInMapBlocks(gid.x, gid.y);
            if (id == 0) {
                return;
            }
            // 挖土
            EventBus.emit('playSound', 'dig');
            let [scar, idx] = this.map.getScarAtGID(gid);
            let scarS: Scar;
            if (idx > -1) {
                scarS = scar.getComponent(Scar) as Scar;
                scarS.beDamaged(damage);
            } else {
                scar = instantiate(this.map.scarFab);
                scar.setPosition(MapUtils.getLocationByGID(gid));
                scar.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
                this.map.node.addChild(scar);
                this.map.scars.push(scar);
                scarS = scar.getComponent(Scar) as Scar;
                scarS.hp = GameData.currentLevelConfig.dirtHP;
                scarS.beDamaged(damage);
            }
            // 土被挖掉显示隐藏道具
            let isGlowingTile = false;
            if (scarS.hp <= 0) {
                this.map.spawnRockFallAtGID(gid);
                // 保存地图数据
                this.map.saveLevelMemo(`${gid.y}:${gid.x}`, { tile: -1 });

                this.currentDirt += 1 * (1 + this.getDirtDelta);
                UserData.currentLevelDirtTotalCount += 1 * (1 + this.getDirtDelta);

                for (let i = 0; i < this.map.glowingTiles.length; i++) {
                    let gGID = MapUtils.getGIDByLocation(this.map.glowingTiles[i].position);
                    if (gGID.equals(gid)) {
                        isGlowingTile = true;
                        this.map.glowingTiles[i].destroy();
                        this.map.glowingTiles.splice(i, 1);
                        i++;
                        break;
                    }
                }
            }
            if (isGlowingTile) {
                // 发光的土，几率低的先判断，这里要先排序
                let chanceKinds = [
                    [GameConfig.blockKinds.ITEM_KEY, GameData.currentLevelConfig.itemKeyChance],
                    [
                        GameConfig.blockKinds.ITEM_DIAMOND,
                        GameData.currentLevelConfig.itemDiamondChance,
                    ],
                    [GameConfig.blockKinds.ITEM_HIDDEN_MONEY, 999], // 保底出金币
                ];
                chanceKinds.sort((a, b) => {
                    return a[1] - b[1] > 0 ? 1 : -1;
                });
                let rn = math.random();
                for (let i = 0; i < chanceKinds.length; i++) {
                    if (rn <= chanceKinds[i][1]) {
                        let [item, ms] = this.map.placeItemAt(
                            scar.position,
                            chanceKinds[i][0],
                            null
                        );
                        item.setPosition(item.position.add(new Vec3(0, -8, 0)));
                        item.getComponent(Sprite).enabled = true; // 挖碎之后显示
                        ms.beDamaged(999);
                        break;
                    }
                }
            } else {
                // card, 一般的土
                if (this.dirtSpawnCoin) {
                    this.map.removeScarAtGID(gid);
                    // 挖什么都会出金币
                    // let pos = new Vec3(scar.position.x, scar.position.y, scar.position.z);
                    // let gid = MapUtils.getGIDByLocation(pos);
                    // this.map.GIDGlowingTiles = [...new Set([...this.map.GIDGlowingTiles, gid])];
                    let [item, ms] = this.map.placeItemAt(
                        scar.position,
                        GameConfig.blockKinds.ITEM_HIDDEN_MONEY,
                        null
                    );
                    item.setPosition(item.position.add(new Vec3(0, -8, 0)));
                    item.getComponent(Sprite).enabled = true; // 挖碎之后显示
                    ms.beDamaged(999);
                }
            }
        }
    }

    // tilesNum: 需要走几格开始上梯子
    public enterExit(useLadder: boolean, tilesNum: number, dir: string) {
        GameConfig.currentGameState = GameConfig.gameStates.SUCCESS;
        let successFn = () => {
            // 进入下一关
            director.loadScene('success');
        };
        let pos = new Vec3(this.node.position.x, this.node.position.y, this.node.position.z);
        if (useLadder) {
            pos = pos.add(new Vec3(0, this.map.tileSize.height * tilesNum, 0));
            if (dir == 'right') {
                pos = pos.add(new Vec3(this.map.tileSize.width * tilesNum, 0, 0));
            } else if (dir == 'left') {
                pos = pos.add(new Vec3(-this.map.tileSize.width * tilesNum, 0, 0));
            }
            tween(this.node)
                .to(1.5, { position: pos })
                .call(() => {
                    successFn();
                })
                .start();
        } else {
            successFn();
        }
    }

    // 进入隐藏房间
    public enterHiddenRoom() {
        console.log('进入隐藏房间！');
        GameConfig.currentGameState = GameConfig.gameStates.ENTERING_PORTAL;

        // 保存游戏数据
        saveUserData();

        // 切换到隐藏房间场景
        director.loadScene('hiddenRoom');
    }

    public worldPos(): Vec3 {
        let playerPos = this.node.parent
            .getComponent(UITransform)
            .convertToWorldSpaceAR(this.node.position);
        playerPos = this.node.parent.parent
            .getComponent(UITransform)
            .convertToNodeSpaceAR(playerPos);
        return playerPos;
    }

    // 掉落伤害是可以叠加的，掉落伤害force=true
    public beDamaged(damage: number, force: boolean, killer: string) {
        if (!force && !this.canBeDamaged) return;
        EventBus.emit('playSound', 'hurt');
        if (killer == 'bomb') {
            damage = damage * (1 + this.bombDamageDelta);
            damage = Math.max(0, damage);
        } else if (killer == 'spike' || killer == 'rock') {
            damage = damage * (1 + this.trapDamageDelta);
            damage = Math.max(0, damage);
        }
        this.lastKiller = killer;
        this.canBeDamaged = false;

        this.hp -= damage;
        if (damage > 0) {
            // 伤害上浮效果
            let playerHurtNode = instantiate(this.map.playerHurtPrafab);
            playerHurtNode.getComponent(PlayerHurtCtl).count = damage;
            this.map.player.addChild(playerHurtNode);

            this.body.getComponent(Sprite).color = new Color().fromHEX('#ce4554');
            this.scheduleOnce(() => {
                this.body.getComponent(Sprite).color = new Color().fromHEX('#ffffff');
            }, 0.1);
        }
        if (this.hp < 0) {
            this.hp = 0;
        }
    }

    // 更新当前关卡的目标数据
    public updateCurrentLevelTargetData(kind: string, num: number, id: number = -1) {
        if (GameData.mode === 'fever') return;

        for (let i = 0; i < GameData.currentLevelTargetData.length; i++) {
            let item = GameData.currentLevelTargetData[i];
            // depth层级任务
            if (kind === 'depth' && item.kind === kind && item.count > item.currentCount) {
                item.currentCount = num;
                this.JudgeCurrentLevelTargetIsDone();
                break;
            }
            // coin金币任务
            if (kind === 'coin' && item.kind === kind && item.count > item.currentCount) {
                item.currentCount = num > item.count ? item.count : num;
                this.JudgeCurrentLevelTargetIsDone();
                break;
            }
            // mine矿石任务
            if (
                kind === 'mine' &&
                item.kind === 'mine' &&
                item.id === id &&
                item.count > item.currentCount
            ) {
                item.currentCount += num;
                this.JudgeCurrentLevelTargetIsDone();
                break;
            }
        }
    }
    // 判断目标是否全部完成
    JudgeCurrentLevelTargetIsDone() {
        if (GameData.currentLevelTargetData.length === 0) {
            return;
        }

        let isDoneAll = [];
        for (let i = 0; i < GameData.currentLevelTargetData.length; i++) {
            let item = GameData.currentLevelTargetData[i];
            if (item.count === item.currentCount) {
                isDoneAll.push(true);
            } else {
                isDoneAll.push(false);
            }
        }

        if (isDoneAll.every(item => item === true)) {
            // 当前关卡 全部完成
            // this.itemHandler.useLadder();

            // 如果关卡目标不是层数才会判定任务完成，是层数的情况在dig方法中已经判定了
            if (this.map.targetDepth == -1) {
                this.enterExit(false, -1, '');
            }
        }
    }

    update(deltaTime: number) {
        if (this.cannotControl()) return;

        // 控制器
        this.autoMoveDelayPassed += deltaTime;
        if (this.isKeyPressing && this.currentKeyPressDir.length > 0) {
            let moveDelta = this.autoMoveDelay - this.autoMoveDelay * this.autoMoveDelayDelta;
            moveDelta = Math.max(moveDelta, this.autoMoveMaxDelay);
            if (this.autoMoveDelayPassed >= moveDelta) {
                this.autoMoveDelayPassed = 0;
                this.arrowAction();
            }
        }

        // 横向移动
        if (this.horiMoving) {
            if (
                Math.abs(this.node.position.x - this.horiMoveStartX) >=
                Math.abs(this.horiMoveTargetDistance) - 3
            ) {
                this.horiMoving = false;
                // this.state = this.states.STAND;
                this.fixLocationAfterMove();
            } else {
                if (this.horiMoveTargetDistance > 0) {
                    this.node.setPosition(
                        new Vec3(
                            this.node.position.x + this.horiMoveSpeed * deltaTime,
                            this.node.position.y,
                            0
                        )
                    );
                } else if (this.horiMoveTargetDistance < 0) {
                    this.node.setPosition(
                        new Vec3(
                            this.node.position.x - this.horiMoveSpeed * deltaTime,
                            this.node.position.y,
                            0
                        )
                    );
                }
            }
        }

        // 物理下落
        this.rigidBody.getWorldCenter(this.worldCenter);
        this.rigidBody.getLinearVelocityFromWorldPoint(this.worldCenter, this.currentVelocity);
        let maxSpeed = this.maxDropSpeed * deltaTime;
        // if (Math.abs(tmpVelocity.x) > maxSpeed) {
        //     this.currentVelocity.x = this.currentVelocity.x > 0 ? maxSpeed : maxSpeed*-1;
        // }
        if (this.currentVelocity.y < -maxSpeed) {
            this.currentVelocity.y = -maxSpeed;
        }
        if (this.currentVelocity.y > maxSpeed) {
            this.currentVelocity.y = maxSpeed;
        }
        if (Math.abs(this.currentVelocity.y) > 0.1) {
            this.bgScript.startScrollFactor = 1;
        } else {
            this.bgScript.startScrollFactor = 0;
        }
        this.rigidBody.linearVelocity = this.currentVelocity;

        // 减伤效果的护盾
        if (this.bombDamageDelta < 0 || this.trapDamageDelta < 0) {
            // 表示此时是减轻伤害
            if (!this.energyShieldNode.active) {
                this.energyShieldNode.active = true;
            }
        } else {
            if (this.energyShieldNode.active) {
                this.energyShieldNode.active = false;
            }
        }

        // HP和灯消耗
        // if (this.hp <= 0) { // 如果用这个表示灯用完可以用血
        if (this.hp <= 0 || this.lampPower <= 0) {
            if (this.lampPower <= 0 && this.hp > 0) {
                this.lastKiller = 'lamp';
            }
            this.bodyAnim.play('die');
            this.weaponNode.active = false;
            this.gamveover();
            return;
        } // else if (this.hp > this.hpInitial) {
        //   this.hp = this.hpInitial;
        //}
        if (this.lampPower > 0) {
            // if (this.lampPower > this.lampInitialPower) this.lampPower = this.lampInitialPower;
            let finalDelta = Math.max(-0.8, this.lampRunSpeedDelta); // 最慢也得是默认速度的20%
            this.lampPower -= this.lampRunSpeed * (1 + finalDelta) * deltaTime;
            this.hpAutoDropPassed = 0;
        } else {
            this.hpAutoDropPassed += deltaTime;
            if (this.hpAutoDropPassed > this.hpAutoDropDuration) {
                this.hpAutoDropPassed = 0;

                this.beDamaged(1, true, 'lamp');
            }
        }
        // 伤害冷却
        if (!this.canBeDamaged) {
            this.beDamagedCollDownPassed += deltaTime;
            if (this.beDamagedCollDownPassed > this.beDamagedCooldown) {
                this.beDamagedCollDownPassed = 0;
                this.canBeDamaged = true;
            }
        }

        // 生成地图
        this.currentGID = MapUtils.getGIDByLocation(this.node.position);
        // if (this.currentGID.y % GameConfig.maxUpdateLevelsRange == 0) {
        this.map.SpawnRoguelikeMaps(this.currentGID);
        // }



        // level UI
        this.ui?.updateLevel(this.currentGID.y);
    }

    gamveover() {
        if (GameConfig.currentGameState != GameConfig.gameStates.GAMEOVER) {
            GameConfig.currentGameState = GameConfig.gameStates.GAMEOVER;
            this.energyShieldNode.active = false;
            let reason = 'You have died!'; //你死了
            if (this.lastKiller == 'bomb') {
                reason = 'You were killed by a bomb!'; // 你被炸弹炸死了
            } else if (this.lastKiller == 'spike') {
                reason = 'The trap killed you!'; // 陷阱害死了你
            } else if (this.lastKiller == 'rock') {
                reason = 'You were crushed to death by a rock!'; // 你被石头压死了
            } else if (this.lastKiller == 'lamp') {
                reason =
                    'The darkness has killed you! You can increase the duration of the oil lamp by upgrading the skill card.'; // 黑暗杀死了你!你可以通过升级技能卡来提升油灯的持续时间。
            } else if (this.lastKiller == 'drop') {
                reason = 'You fell to your death!'; //你摔死了!
            }
            this.scheduleOnce(() => {
                EventBus.emit('playSound', 'die');
                this.ui.showGameover(reason);
            }, 1);
        }
    }

    public getPlayerBattleInfo() {
        // 基础攻击（初始 + 升级可能） + 武器攻击
        let power = UserData.power + GameConfig.weapons[UserData.currentWeapon].power;
        // 基础生命(初始 + 升级）
        let hp = UserData.heart;
        // 矿灯(初始 + 升级）
        let light = UserData.light;
        if (GameData.mode === 'fever') {
            light = GameData.currentLevelConfig.light;
        }

        // 装备卡
        for (let i = 0; i < UserData.equippedCards.length; i++) {
            let gearCardConf = GameConfig.gearCards[UserData.equippedCards[i]];
            if (gearCardConf.kind === GameConfig.cardsKinds.POWER) {
                power += gearCardConf.value;
            } else if (gearCardConf.kind === GameConfig.cardsKinds.HP) {
                hp += gearCardConf.value;
            } else if (gearCardConf.kind === GameConfig.cardsKinds.LAMP) {
                light += gearCardConf.value;
            }
        }

        return { power: power, hp: hp, light: light };
    }
}
