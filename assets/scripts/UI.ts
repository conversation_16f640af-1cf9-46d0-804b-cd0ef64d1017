import {
  _decorator,
  Component,
  director,
  Input,
  instantiate,
  Label,
  Node,
  Prefab,
  ProgressBar,
  Sprite,
  SpriteFrame,
  tween,
  UITransform,
  Vec3,
//   Widget,
} from "cc";
import { AlertCtl } from "./AlertCtl";
import { BcakItem } from "./Backpack/BcakItem";
import { EventBus } from "./EventBus";
import { GameConfig } from "./GameConfig";
import { GameData } from "./GameData";
import { Player } from "./Player";
import { btnClick, saveMoneyToWechat, saveUserData } from "./Utils/Tools";
import { UserData } from "./Utils/UserData";
import { CommonAssetsCtl } from "./v1/Common/CommonAssetsCtl";
import { GameTargetTempCtl } from "./v1/Level/GameTargetTempCtl";
// import { coinCtl } from "./Game/coinCtl";
import { OneCard } from "./UI/OneCard";
// import { AdResult } from "./AdMob/AdMobRewardedVideo";
import { GameManager } from "./GameManager";
import { Map } from "./Map";
// import { Chapters } from "./Chapters/Chapters";
// import { resources } from "cc";
// import { FeverChapters } from "./FeverChapters/FeverChapters";
// import { TiledMapAsset } from "cc";
import { Global } from "./Global";
const { ccclass, property } = _decorator;

@ccclass("UI")
export class UI extends Component {
  @property(Prefab)
  heartFab: Prefab;

  @property(Prefab)
  backpackItem: Prefab;

  @property(Prefab)
  oneCardPrefab: Prefab;

  // @property(Node)
  // upBoom: Node;
  // @property(Node)
  // downBoom: Node;
  // @property(Node)
  // Boom: Node;
  // @property(Node)
  // Ladder: Node;

  @property(SpriteFrame)
  upBoomIcon: SpriteFrame;
  @property(SpriteFrame)
  downBoomIcon: SpriteFrame;
  @property(SpriteFrame)
  BoomIcon: SpriteFrame;
  @property(SpriteFrame)
  LadderIcon: SpriteFrame;

  @property(Node)
  LevelS: Node;
  @property(SpriteFrame)
  playerFrame: SpriteFrame;

  @property(Prefab)
  AlertPrefab: Prefab;

  @property(Node)
  BackBtnNode: Node;
  @property(Node)
  AdNode: Node;

  @property(SpriteFrame)
  missionFailedTitleBg: SpriteFrame;
  @property(SpriteFrame)
  congratulationsTitleBg: SpriteFrame;

  // 通用图片资源
  public CommonAssets;

  private player: Player;
  // private hpContainerNode: Node;
  private hpNumNode: Node;
  private lampNumNode: Node;
  private dirtNumNode: Node;
  private diamondNumNode: Node;
  private keysNumNode: Node;
  private expProgressNode: Node;
//   private moneyLabel: Node;
  private levelLabel: Node;
//   private playerNode: Node;

//   private currentMoney = 0;
  private isAd = false;

  public gameState: string = null;

  private targetAreaLayoutNode: Node;
  @property(Prefab)
  gameTargetTempPrefab: Prefab;

  public map: Map;
  private rewardsExpProgressNode: Node;
  private levelUpPopupNode: Node;

  private gameManager: GameManager;

  start() {
    this.CommonAssets = director
      .getScene()
      .getChildByPath("Canvas/CommonAssets")
      .getComponent(CommonAssetsCtl);
    this.gameManager = director
      .getScene()
      .getChildByPath("Canvas/GameManager")
      .getComponent(GameManager);
    this.node.getComponent(UITransform).priority = GameConfig.UIPriority.allUI;

    this.map = director
      .getScene()
      .getChildByPath("Canvas/TiledMap")
      .getComponent(Map);
    this.player = director
      .getScene()
      .getChildByPath("Canvas/TiledMap/Player")
      ?.getComponent(Player);
    // this.playerNode = director
    //   .getScene()
    //   .getChildByPath("Canvas/TiledMap/Player");
    this.hpNumNode = this.node.getChildByPath("Status/HPNum");
    this.lampNumNode = this.node.getChildByPath("Status/LampNum");
    this.dirtNumNode = this.node.getChildByPath("Dirt/Label");
    this.diamondNumNode = this.node.getChildByPath("Diamond/Label");
    this.keysNumNode = this.node.getChildByPath("Keys/Label");
    this.expProgressNode = this.node.getChildByPath("Status/ExpBar");
    this.expProgressNode.getComponent(ProgressBar).progress = 0;
    this.targetAreaLayoutNode = this.node.getChildByPath(
      "Status/TargetArea/Layout"
    );

    if (GameData.mode === "general") {
      this.initTargetArea();
    } else {
      this.node.getChildByPath("Status/TargetArea").active = false;
      this.node.getChildByPath("Status/Rock_08").active = false;
      this.node.getChildByPath("Status/ExpBar").active = false;

      // 挖什么都会出金币
      this.player.dirtSpawnCoin = true;
    }

    // this.moneyLabel = this.node.getChildByPath("Bag/Money");
    this.levelLabel = this.node.getChildByPath("Level");
    // this.initBoom();

    this.rewardsExpProgressNode =
      this.node.getChildByPath("RewardsExpProgress");
    this.rewardsExpProgressNode.getComponent(ProgressBar).progress = 0;
    this.rewardsExpProgressNode.active = false;

    this.levelUpPopupNode = this.node.getChildByPath("LevelUpPopup");
    this.levelUpPopupNode.active = false;
    this.levelUpPopupNode.getChildByName("Btn").active = false;

    // 清空UserData当前关卡数据
    this.gameManager.clearCurrentLevelUserData();
    // 初始化UserData当前关卡数据
    UserData.currentLevelBoomDps = GameData.currentLevelConfig.bombDamage;

    //游戏结束
    // this.BackBtnNode.on(Input.EventType.TOUCH_END, this.onBackBtnClick, this);
    // this.AdNode.on(Input.EventType.TOUCH_END, this.onAdBtnClick, this);

    EventBus.on("adSuccess", this.adSuccess, this);

    if (GameData.mode === "fever") {
      this.node.getChildByPath("FeverDeadLine").active = true;
      this.startFeverDeadLine();
    } else {
      this.node.getChildByPath("FeverDeadLine").active = false;
    }
  }

  startFeverDeadLine() {
    GameConfig.currentGameState = GameConfig.gameStates.PAUSED;
    let labelNode = this.node.getChildByPath("FeverDeadLine");
    labelNode.getComponent(Label).string = "Fever Mode";
    labelNode.setScale(new Vec3(3, 3, 3));

    tween(labelNode)
      .to(0.3, { scale: new Vec3(1, 1, 1) }, { easing: "sineOut" })
      .delay(0.5)
      .call(() => {
        labelNode.getComponent(Label).string = "3";
        labelNode.setScale(new Vec3(3, 3, 3));
      })
      .to(0.3, { scale: new Vec3(1, 1, 1) }, { easing: "sineOut" })
      .delay(0.5)
      .call(() => {
        labelNode.getComponent(Label).string = "2";
        labelNode.setScale(new Vec3(3, 3, 3));
      })
      .to(0.3, { scale: new Vec3(1, 1, 1) }, { easing: "sineOut" })
      .delay(0.5)
      .call(() => {
        labelNode.getComponent(Label).string = "1";
        labelNode.setScale(new Vec3(3, 3, 3));
      })
      .to(0.3, { scale: new Vec3(1, 1, 1) }, { easing: "sineOut" })
      .delay(0.5)
      .call(() => {
        labelNode.active = false; // 隐藏Label
        console.log("Countdown finished!");
        GameConfig.currentGameState = GameConfig.gameStates.RUNNING; // 开始游戏
      })
      .start();
  }

  initTargetArea() {
    this.targetAreaLayoutNode.removeAllChildren();

    for (let i = 0; i < GameData.currentLevelConfig.targets.length; i++) {
      let targetTempNode = instantiate(this.gameTargetTempPrefab);
      let targetKind = GameData.currentLevelConfig.targets[i].kind;
      let count = GameData.currentLevelConfig.targets[i].count;

      targetTempNode.getComponent(GameTargetTempCtl).kind = targetKind;
      targetTempNode.getComponent(GameTargetTempCtl).count = count;

      targetTempNode.getChildByName("Bg").getComponent(Sprite).spriteFrame =
        this.CommonAssets[`levelTarget${targetKind}BgFrame`];
      targetTempNode
        .getChildByPath("Bg/Icon")
        .getComponent(Sprite).spriteFrame =
        this.CommonAssets[`levelTarget${targetKind}IconFrame`];
      targetTempNode
        .getChildByName("Num")
        .getComponent(Label).string = `0/${count}`;

      if (targetKind === "mine") {
        let id = GameData.currentLevelConfig.targets[i].id;
        targetTempNode.getComponent(GameTargetTempCtl).id = id;
        targetTempNode
          .getChildByPath("Bg/Icon")
          .getComponent(Sprite).spriteFrame =
          this.CommonAssets.GetMineFrame(id);
        if (id === GameConfig.blockKinds.MINE_A19) {
          // targetTempNode.getChildByPath("Bg/Icon").setScale(0.9, 0.9);
        } else {
          targetTempNode.getChildByPath("Bg/Icon").setScale(0.8, 0.8);
          targetTempNode.getChildByPath("Bg/Icon").setPosition(0, 2, 0);
          targetTempNode.setScale(1.2, 1.2);
        }
      } else if (targetKind === "depth") {
        targetTempNode.getChildByPath("Bg/Icon").setScale(0.8, 0.8);
        targetTempNode.getChildByPath("Bg/Icon").setPosition(0, 2, 0);
        targetTempNode.setScale(1.2, 1.2);
      } else if (targetKind === "coin") {
        // targetTempNode.getChildByPath("Bg/Icon").setScale(1.2, 1.2);
        targetTempNode.getChildByPath("Bg/Icon").setPosition(0, 4, 0);
      }

      this.targetAreaLayoutNode.addChild(targetTempNode);
    }

    // 描述
    let desp = "";
    for (let i = 0; i < GameData.currentLevelConfig.mines.kinds.length; i++) {
      let mineID = GameData.currentLevelConfig.mines.kinds[i];
      if (GameData.currentLevelConfig.mines.hasOwnProperty(mineID)) {
        let minLevel = GameData.currentLevelConfig.mines[mineID].minLevel;
        let mineName = GameConfig.mineName[mineID];

        desp += `${mineName} below ${minLevel} levels;`;
      }
    }

    this.node
      .getChildByPath("Status/TargetArea/Desp")
      .getComponent(Label).string = desp;
  }

  adSuccess(type: string) {
    if (type === "addResurgence") {
      this.resurgence();
    }

    if (type === "gameOver") {
      EventBus.emit("coinToEnd");
      this.runPackItemToEnd();
      this.scheduleOnce(() => {
        EventBus.emit("settle", "success", GameData.mode);
        this.scheduleOnce(() => {
          UserData.nextScene = "town";
          UserData.nextSceneAction = null;
          director.loadScene("loading");
        }, 0.5);
      }, 1);
    }

    if (type === "success") {
      EventBus.emit("addCoin");
      this.runPackItemToAdd();
      this.scheduleOnce(() => {
        EventBus.emit("settle", "success", GameData.mode);
        this.scheduleOnce(() => {
          UserData.nextScene = "town";
          UserData.nextSceneAction = null;
          director.loadScene("loading");
        }, 0.5);
      }, 1);
    }
  }

  update(_deltaTime: number) {
    if (GameConfig.currentGameState == GameConfig.gameStates.GAMEOVER) {
      return;
    }

    if (!this.targetAreaLayoutNode) return;
    // 目标栏显示

    if (GameData.mode === "general") {
      for (let i = 0; i < this.targetAreaLayoutNode.children.length; i++) {
        let child = this.targetAreaLayoutNode.children[i];
        let targetKind = child.getComponent(GameTargetTempCtl).kind;
        for (let j = 0; j < GameData.currentLevelTargetData.length; j++) {
          let item = GameData.currentLevelTargetData[j];
          if (targetKind === item.kind) {
            if (targetKind === "mine") {
              let targetID = child.getComponent(GameTargetTempCtl).id;
              // kind是"mine"的情况，再判断id
              if (targetID === item.id) {
                child
                  .getChildByName("Num")
                  .getComponent(
                    Label
                  ).string = `${item.currentCount}/${item.count}`;
              }
            } else {
              // kind是"depth"和"coin"的情况，同种类型只会有一个目标
              child
                .getChildByName("Num")
                .getComponent(
                  Label
                ).string = `${item.currentCount}/${item.count}`;
            }
          }
        }
      }
    }
    if (this.player) {
      this.hpNumNode.getComponent(Label).string = parseInt(
        this.player.hp.toString()
      ).toString();
      this.lampNumNode.getComponent(Label).string = parseInt(
        this.player.lampPower.toString()
      ).toString();
      this.dirtNumNode.getComponent(Label).string =
        this.player.currentDirt.toString();

      this.expProgressNode.getComponent(ProgressBar).progress =
        1 - this.player.currentExp / this.player.fullExp;
    }
    this.diamondNumNode.getComponent(Label).string =
      UserData.currentLevelDiamondCount.toString();
    this.keysNumNode.getComponent(Label).string =
      UserData.currentLevelKeyCount.toString();
  }

  public updateLevel(num: number) {
    const totalDepth = this.map.targetDepth;
    this.levelLabel.getChildByPath("Node/Label").getComponent(Label).string =
      totalDepth.toString();
    const label = this.levelLabel
      .getChildByPath("ProgressBar/Bar/Node/Label")
      .getComponent(Label);
    if (num.toString() === label.string) return;

    const progressBar = this.levelLabel.getChildByName("ProgressBar");
    progressBar.getComponent(ProgressBar).progress = num / totalDepth;

    const marker = progressBar.getChildByPath("Bar/Node");

    marker.setPosition(
      0,
      -(
        progressBar.getChildByPath("Bar").getComponent(UITransform).height + 10
      ),
      0
    );

    label.string = num.toString();

    // 更新任务完成情况
    this.player.updateCurrentLevelTargetData("depth", num);
  }

  public showGameover(txt: string, isShowAd: boolean = true) {
    if (GameData.mode === "fever") {
      //fever模式不能看广告
      this.settle("gameover");
      return;
    }

    if (UserData.currentLevelResurgenceCardID !== -1) {
      UserData.currentLevelResurgenceCardID = -1;
      this.resurgence();
      return;
    }

    this.gameState = "gameover";
    if (this.isAd) {
      this.scheduleOnce(() => {
        this.settle("gameover");
      }, 0.5);
    } else {
      this.scheduleOnce(() => {
        const node = instantiate(this.AlertPrefab);
        node.getComponent(UITransform).priority =
          GameConfig.UIPriority.lampMask;
        const com = node.getComponent(AlertCtl);
        com.title = "Message";
        com.body = txt;
        com.isClose = false;
        if (isShowAd) {
          com.comfirmText = "(AD)Revival"; //复活
          com.cancelText = "Cancel";
          com.onComfirm = () => {
            // EventBus.emit("show-rewarded-ad", "addResurgence");
            this.isAd = true;
            // this.map.adService.showAd((result, rewardType, amount) => {
            //     if (result === AdResult.EARNED_REWARD) {
            //       console.log(`复活：用户完整观看了广告，获得奖励: ${rewardType}, ${amount}`);
            //       this.resurgence();
            //     }
            // });
            // com.onClose()
            console.log("mission  failed");
            this.resurgence();
            com.onClose();
          };
          com.onCancel = () => {
            com.onClose();
            this.settle("gameover");
          };
        } else {
          com.comfirmText = "Confirm"; //  确定
          com.showCancel = false;
          com.onComfirm = () => {
            com.onClose();
            this.settle("gameover");
          };
          com.onCancel = () => {
            com.onClose();
            this.settle("gameover");
          };
        }
        this.node.addChild(node);
      }, 0.5);
    }
  }

  private resurgence() {
    if (this.player.lampPower < Math.floor(this.player.lampInitialPower / 2)) {
      this.player.lampPower = Math.floor(this.player.lampInitialPower / 2);
    }

    this.player.hp = 3;

    this.player.canBeDamaged = false;
    // this.playerNode.getChildByName("Body").getComponent(Sprite).spriteFrame = this.playerFrame;
    this.player.bodyAnim.play("idle");
    this.player.weaponNode.active = true;
    GameConfig.currentGameState = null;
  }

  public showSuccess() {
    this.gameState = "success";
    this.scheduleOnce(() => {
      if (UserData.currentLevelCallLadderCardID !== -1) {
        this.gameState = "gameover"; // 使用了“呼叫梯子”卡牌，游戏失败
        this.settle("gameover", "Mission Failed"); //成功脱出
      } else {
        this.settle("success", "Congratulations");
      }
    }, 0.5);
  }

  public showNextPortal() {
    this.gameState = "success";
    // TODO 新的章节结构"0-0", "3-1"
    this.scheduleOnce(() => {
      // if (GameData.currentMapID === "map1") {
      //     UserData.userLocks = ["1", "2"];
      // } else if (GameData.currentMapID === "map2") {
      //     UserData.userLocks = ["1", "2", "3"];
      // }
      saveUserData();
      this.settle("success", "解锁下一级别矿坑");
    }, 0.5);
  }



  private isBackBtnClick = false;
  private isAdBtnClick = false;

  // 结算时，看广告金币翻倍
  private onAdBtnClick(startValue, rewardCount: number = 0) {
    btnClick(this.AdNode);
    if (this.isAdBtnClick) {
      return;
    }
    this.isAdBtnClick = true;

    // this.scheduleOnce(() => {
    //     this.isAdBtnClick = false;
    // }, 1);

    // this.map.adService.showAd((result, rewardType, amount) => {
    //     if (result === AdResult.EARNED_REWARD) {
    //         console.log(`游戏结算双倍金币：用户完整观看了广告，获得奖励: ${rewardType}, ${amount}`);

    //         let endValue = startValue * 2;
    //         let counter = { value: startValue };
    //         tween(counter).to(2, { value: endValue }, {
    //             easing: 'sineOut', // 使用平滑的缓动函数
    //             onUpdate: () => {
    //                 // 更新 Label 文本，保留整数
    //                 let s = ""
    //                 if (rewardCount > 0) {
    //                     s = `Rewards   +${rewardCount}\nDiscovery +${Math.floor(counter.value)}`;
    //                 } else {
    //                     s = `Discovery +${Math.floor(counter.value)}`;
    //                 }
    //                 this.node.getChildByPath("Bg/Content/Label").getComponent(Label).string = s;
    //             },
    //         }).start();
    //         this.AdNode.active = false;
    //     }
    // });
    let endValue = startValue * 2;
    let counter = { value: startValue };
    tween(counter)
      .to(
        2,
        { value: endValue },
        {
          easing: "sineOut", // 使用平滑的缓动函数
          onUpdate: () => {
            // 更新 Label 文本，保留整数
            let s = "";
            if (rewardCount > 0) {
              s = `Rewards   +${rewardCount}\nDiscovery +${Math.floor(
                counter.value
              )}`;
            } else {
              s = `Discovery +${Math.floor(counter.value)}`;
            }
            this.node
              .getChildByPath("Bg/Content/Label")
              .getComponent(Label).string = s;
          },
        }
      )
      .start();
    this.AdNode.active = false;
    UserData.currentLevelAdDoubleCoinRatio = 2; // 广告翻倍

    // if (this.gameState === "gameover") {
    //     EventBus.emit("show-rewarded-ad", "gameOver");
    // } else {
    //     EventBus.emit("show-rewarded-ad", "success");
    // }
  }

  private onBackBtnClick() {
    btnClick(this.BackBtnNode);
    if (this.isBackBtnClick) {
      return;
    }
    this.isBackBtnClick = true;

    this.scheduleOnce(() => {
      this.isBackBtnClick = false;
    }, 1);

    if (this.gameState === "gameover") {
      EventBus.emit("coinToEnd");
      // EventBus.emit("subCoin");
      // this.runPackItemToWarehouse();
      EventBus.emit("settle", "gameover", GameData.mode);
      saveMoneyToWechat();
    } else {
      EventBus.emit("coinToEnd");
      // this.runPackItemToEnd();
      EventBus.emit("settle", "success", GameData.mode);
      saveMoneyToWechat();
    }

    if (UserData.nextMode === "fever") {
      GameData.globalPersistNode
        .getChildByPath("Global")
        .getComponent(Global)
        .startGame();
    } else {
      UserData.nextScene = "v1/level";
      UserData.nextSceneAction = "play1Bgm";
      director.loadScene("loading");
    }
  }

  public rewardsArray = []; // 通关奖励的物品
  public digProductsArray = []; // 自己挖的物品
  // digProductsArray与rewardsArray结构一致，自己构建如下：
  // [
  //     {kind: "coin", count: 100},
  //     {kind: "diamond", count: 1},
  //     {kind: "key", count: 1}, // 钥匙和宝箱配对，分三个等级的钥匙
  //     {kind: "cardPart", id: 1_01, count: 1}, // 卡片碎片，id为卡片编号
  //     {kind: "card", id: 1_01, count: 1}, // 卡片，id为卡片编号
  //     {kind: "exp", count: 100}, // 奖励经验值
  // ]
  // 结算 backpack 下移 coin 下移，仓库出现抖动
  public settle(type: string, desp: string = "") {
    this.rewardsArray = GameData.currentLevelConfig.rewards;
    let digProductCoinNum = Number(
      this.node.getChildByPath("Coin/Label").getComponent(Label).string
    );
    if (digProductCoinNum > 0) {
      this.digProductsArray.push({ kind: "coin", count: digProductCoinNum });
    }
    if (UserData.currentLevelDiamondCount > 0) {
      this.digProductsArray.push({
        kind: "diamond",
        count: UserData.currentLevelDiamondCount,
      });
    }
    if (UserData.currentLevelKeyCount > 0) {
      this.digProductsArray.push({
        kind: "key",
        count: UserData.currentLevelKeyCount,
      });
    }

    this.node.getChildByPath("Bg").active = true;
    this.node.getChildByPath("Bg").getComponent(UITransform).priority =
      GameConfig.UIPriority.lampMask;
    if (type === "gameover") {
      // 如果任务失败，将经验添加到自己挖的物品里，按当前挖土总数量的5%计算
      if (GameData.mode === "general") {
        let exp = Math.floor(UserData.currentLevelDirtTotalCount * 0.05);
        if (exp > 0) {
          this.digProductsArray.push({ kind: "exp", count: exp });
        }
      }

      if (this.digProductsArray.length > 0) {
        this.showDigProducts("Mission Failed"); //闯关失败
      } else {
        // 没有挖到任何东西
        this.node.getChildByPath("Bg").active = false;
        UserData.nextScene = "v1/level";
        UserData.nextSceneAction = "play1Bgm";
        director.loadScene("loading");
      }
      // this.AdNode.getChildByPath("Node").getChildByPath("Label").getComponent(Label).string =
      //     "不减半";
    } else {
      // type === "success"
      this.showRewards(desp);
      // this.AdNode.getChildByPath("Node").getChildByPath("Label").getComponent(Label).string =
      //     "双倍";
    }
  }

  public showRewards(desp: string = "") {
    this.AdNode.active = false;
    this.node.getChildByPath("Bg/Image").active = true;
    this.node.getChildByPath("Bg/Content").active = true;

    this.node.getChildByPath("Bg/Content/Label").active = false;
    let bgContentLabelActive = false;

    this.rewardsExpProgressNode.active = false;

    let despTxt = desp === "" ? "Congratulations" : desp;
    this.node.getChildByPath("Bg/Title/Label").getComponent(Label).string =
      despTxt;
    // 任务失败 不显示this.missionFailedTitleBg
    this.node.getChildByPath("Bg/Title/Bg").getComponent(Sprite).spriteFrame =
      despTxt == "Congratulations" ? this.congratulationsTitleBg : null;

    let thisReward = this.rewardsArray[0];
    this.node
      .getChildByPath("Bg/Content/Label")
      .getComponent(Label).string = `Rewards +${thisReward.count}`;
    let rewardNode = this.node.getChildByPath("Bg/Content/Reward");
    rewardNode.removeAllChildren();

    let tweenScaleTo = new Vec3(2, 2, 2);
    let digProductsCount = -1;
    // 图标
    if (thisReward.kind === "cardPart") {
      let rare = GameConfig.cards[thisReward.id].rare;
      rewardNode.getComponent(Sprite).spriteFrame =
        this.CommonAssets[`levelReward${thisReward.kind}${rare}IconFrame`];
      rewardNode.setScale(new Vec3(6, 6, 6));
      bgContentLabelActive = true;
      EventBus.emit("playSound", "settleOther");
    } else if (thisReward.kind === "card") {
      let id = thisReward.id;

      let cardWidth = 265;
      let cardHeight = 330;
      let cardNode = instantiate(this.oneCardPrefab);
      cardNode.getComponent(OneCard).cardID = id;
      cardNode.getComponent(OneCard).from = "game/success";
      cardNode.getComponent(OneCard).cardWidth = cardWidth;
      cardNode.getComponent(OneCard).cardHeight = cardHeight;
      rewardNode.addChild(cardNode);

      rewardNode.getComponent(Sprite).spriteFrame = null;
      rewardNode.setScale(new Vec3(2, 2, 2));
      tweenScaleTo = new Vec3(1, 1, 1);
      EventBus.emit("playSound", "settleOther");
    } else {
      rewardNode.getComponent(Sprite).spriteFrame =
        this.CommonAssets[`levelReward${thisReward.kind}IconFrame`];
      if (thisReward.kind === "coin") {
        rewardNode.getComponent(Sprite).spriteFrame =
          this.CommonAssets.levelRewardMultiCoinsIconFrame;
        EventBus.emit("playSound", "settleCoin");
      } else if (thisReward.kind === "diamond") {
        EventBus.emit("playSound", "settleOther");
      } else if (thisReward.kind === "key") {
        EventBus.emit("playSound", "settleOther");
      }
      rewardNode.setScale(new Vec3(6, 6, 6));
      bgContentLabelActive = true;

      // 如果有自己挖的物品，显示数量
      if (this.digProductsArray.length > 0) {
        for (let i = 0; i < this.digProductsArray.length; i++) {
          if (this.digProductsArray[i].kind === thisReward.kind) {
            digProductsCount = this.digProductsArray[i].count;
            this.node
              .getChildByPath("Bg/Content/Label")
              .getComponent(
                Label
              ).string = `Rewards   +${thisReward.count}\nDiscovery +${digProductsCount}`;
            this.digProductsArray.splice(i, 1); // 删除已显示的物品
            break;
          }
        }
      }
    }

    tween(rewardNode)
      .to(
        0.3,
        {
          scale: tweenScaleTo,
        },
        {
          onComplete: () => {
            this.node.getChildByPath("Bg/Content/Label").active =
              bgContentLabelActive;
            if (digProductsCount !== -1) {
              if (
                UserData.currentLevelAddHalfCardID !== -1 ||
                UserData.currentLevelLampHalfCardID !== -1
              ) {
                let startValue = digProductsCount;
                let endValue = 0;
                let duration = 1; // 动画持续时间（秒）
                let counter = { value: startValue };

                // 当前关卡如果完成任务，获得资源+50%
                if (UserData.currentLevelAddHalfCardID !== -1) {
                  Math.floor(
                    digProductsCount *
                      GameConfig.cards[UserData.currentLevelAddHalfCardID]
                        .value1
                  );
                }

                // 当前关卡使用了本局矿灯时间减半，但是获取的收益会翻倍
                if (UserData.currentLevelLampHalfCardID !== -1) {
                  endValue = Math.floor(
                    digProductsCount *
                      GameConfig.cards[UserData.currentLevelLampHalfCardID]
                        .value1
                  );
                }
                tween(counter)
                  .to(
                    duration,
                    { value: endValue },
                    {
                      easing: "sineOut", // 使用平滑的缓动函数
                      onUpdate: () => {
                        // 更新 Label 文本，保留整数
                        this.node
                          .getChildByPath("Bg/Content/Label")
                          .getComponent(Label).string = `Rewards   +${
                          thisReward.count
                        }\nDiscovery +${Math.floor(counter.value)}`;
                      },
                      onComplete: () => {
                        this.node.getChildByPath("Btn").active = true;
                        this.BackBtnNode.getChildByName("Label").getComponent(
                          Label
                        ).string =
                          this.rewardsArray.length === 1 ? "Done" : "Next";
                        this.BackBtnNode.parent.getComponent(
                          UITransform
                        ).priority = GameConfig.UIPriority.lampMask + 1;
                        this.isBackBtnClick = false;
                        this.BackBtnNode.on(
                          Input.EventType.TOUCH_END,
                          this.onCircleRewardBackBtnClick,
                          this
                        );

                        if (thisReward.kind === "coin") {
                          // 显示看广告双倍金币按钮
                          this.AdNode.active = true;
                          this.isAdBtnClick = false;

                          this.AdNode.on(
                            Input.EventType.TOUCH_END,
                            () => {
                              this.onAdBtnClick(
                                digProductsCount,
                                thisReward.count
                              );
                            },
                            this
                          );
                        }
                      },
                    }
                  )
                  .start();
              } else {
                this.node.getChildByPath("Btn").active = true;
                this.BackBtnNode.getChildByName("Label").getComponent(
                  Label
                ).string = this.rewardsArray.length === 1 ? "Done" : "Next";
                this.BackBtnNode.parent.getComponent(UITransform).priority =
                  GameConfig.UIPriority.lampMask + 1;
                this.isBackBtnClick = false;
                this.BackBtnNode.on(
                  Input.EventType.TOUCH_END,
                  this.onCircleRewardBackBtnClick,
                  this
                );
                if (thisReward.kind === "coin") {
                  // 显示看广告双倍金币按钮
                  this.AdNode.active = true;
                  this.isAdBtnClick = false;

                  this.AdNode.on(
                    Input.EventType.TOUCH_END,
                    () => {
                      this.onAdBtnClick(digProductsCount, thisReward.count);
                    },
                    this
                  );
                }
              }
            } else {
              this.node.getChildByPath("Btn").active = true;
              this.BackBtnNode.getChildByName("Label").getComponent(
                Label
              ).string = this.rewardsArray.length === 1 ? "Done" : "Next";
              this.BackBtnNode.parent.getComponent(UITransform).priority =
                GameConfig.UIPriority.lampMask + 1;
              this.isBackBtnClick = false;
              this.BackBtnNode.on(
                Input.EventType.TOUCH_END,
                this.onCircleRewardBackBtnClick,
                this
              );
            }

            // 如果是经验值，显示经验条动画
            if (thisReward.kind === "exp") {
              this.showRewardsExpProgress(thisReward.count);
            }
          },
        }
      )
      .start();
  }

  private rewardsExpProgressTweenData = [];
  showRewardsExpProgress(incExp: number) {
    if (GameData.mode === "fever") return;

    this.rewardsExpProgressNode.active = true;
    this.rewardsExpProgressNode.getComponent(UITransform).priority =
      GameConfig.UIPriority.lampMask + 1;

    EventBus.emit("playSound", "settleExp");

    let currentGrade = UserData.grade;
    this.rewardsExpProgressNode
      .getChildByName("Label")
      .getComponent(Label).string = `Lv.${currentGrade}`;

    // 当前等级 经验
    let currentExp = UserData.currentExp;
    let currentLevelStartExp = GameConfig.levelupUnlocks[currentGrade - 1].exp;
    let nextLevelStartExp = GameConfig.levelupUnlocks[currentGrade].exp;
    let currentProgress =
      1 -
      (currentExp - currentLevelStartExp) /
        (nextLevelStartExp - currentLevelStartExp);
    this.rewardsExpProgressNode.getComponent(ProgressBar).progress =
      currentProgress;

    // 增加之后的经验值
    let afterExp = currentExp + incExp;
    // 长了几级
    let incLevelsCount = 0;
    // 增长到的那一级到下一级经验值的差值
    let theLevelExpDur = 0;
    // 看增加经验值，增加到哪一级
    for (let i = 0; i < 10; i++) {
      let exp = GameConfig.levelupUnlocks[UserData.grade - 1 + i].exp;
      let nextExps = GameConfig.levelupUnlocks[UserData.grade + i].exp;

      if (afterExp >= exp && afterExp < nextExps) {
        incLevelsCount = i;
        theLevelExpDur = nextExps - exp;
        break;
      }
    }
    // 增长到的那一级最终到哪里
    let theLevelIncExp =
      afterExp -
      GameConfig.levelupUnlocks[UserData.grade - 1 + incLevelsCount].exp;
    let endProgress = 1 - theLevelIncExp / theLevelExpDur;
    // 正好升级
    if (theLevelIncExp === 0) {
      endProgress = 0;
    }

    // 遍历
    for (let i = 0; i <= incLevelsCount; i++) {
      if (incLevelsCount > 0) {
        // 升了几级
        if (i === 0) {
          this.rewardsExpProgressTweenData.push({
            start: currentProgress,
            end: 0,
            level: currentGrade + i,
            isShowLevelPopup: false,
          });
        } else if (i === incLevelsCount) {
          // 最后一级
          currentProgress = 1;
          if (endProgress === 0) {
            endProgress = 1;
          }

          this.rewardsExpProgressTweenData.push({
            start: currentProgress,
            end: endProgress,
            level: currentGrade + i,
            isShowLevelPopup: true, // 如果升级且是最后一级显示升级弹窗
          });
        } else {
          this.rewardsExpProgressTweenData.push({
            start: 1,
            end: 0,
            level: currentGrade + i,
            isShowLevelPopup: false,
          });
        }
      } else {
        // 没升级
        this.rewardsExpProgressTweenData.push({
          start: currentProgress,
          end: endProgress,
          level: currentGrade + i,
          isShowLevelPopup: false, // 如果升级且是最后一级显示升级弹窗
        });
      }
    }

    this.rewardsExpProgressTween();
  }

  rewardsExpProgressTween() {
    if (this.rewardsExpProgressTweenData.length === 0) return;

    let start = this.rewardsExpProgressTweenData[0].start;
    let end = this.rewardsExpProgressTweenData[0].end;
    let level = this.rewardsExpProgressTweenData[0].level;
    let isShowLevelPopup = this.rewardsExpProgressTweenData[0].isShowLevelPopup;
    this.rewardsExpProgressNode
      .getChildByName("Label")
      .getComponent(Label).string = `Lv.${level}`;
    this.rewardsExpProgressNode.getComponent(ProgressBar).progress = start;
    tween(this.rewardsExpProgressNode.getComponent(ProgressBar))
      .to(
        0.8,
        {
          progress: end,
        },
        {
          easing: "sineInOut",
          onComplete: () => {
            this.rewardsExpProgressTweenData.shift();

            if (isShowLevelPopup) {
              this.showLevelPopup(level);
            }

            this.rewardsExpProgressTween();
          },
        }
      )
      .start();
  }

  showLevelPopup(level: number) {
    this.levelUpPopupNode.active = true;
    this.levelUpPopupNode.getComponent(UITransform).priority =
      GameConfig.UIPriority.lampMask + 2;

    let labelNode = this.levelUpPopupNode.getChildByPath("Content/Label");
    labelNode.getComponent(Label).string = `Lv${level}`;

    labelNode.setScale(new Vec3(3, 3, 3));

    EventBus.emit("playSound", "upgrade");

    tween(labelNode)
      .to(
        0.8,
        {
          scale: new Vec3(1, 1, 1),
        },
        {
          onComplete: () => {
            this.levelUpPopupNode.getChildByName("Btn").active = true;
            this.levelUpPopupNode
              .getChildByName("Btn")
              .on(Input.EventType.TOUCH_END, this.onClickLevelUpPopupBtn, this);
          },
        }
      )
      .start();
  }

  onClickLevelUpPopupBtn() {
    btnClick(this.levelUpPopupNode.getChildByName("Btn"));
    this.levelUpPopupNode.active = false;
    this.levelUpPopupNode
      .getChildByName("Btn")
      .off(Input.EventType.TOUCH_END, this.onClickLevelUpPopupBtn, this);
  }

  public showDigProducts(desp: string = "") {
    this.AdNode.active = false;
    this.node.getChildByPath("Bg/Image").active = true;
    this.node.getChildByPath("Bg/Content").active = true;

    this.node.getChildByPath("Bg/Content/Label").active = false;
    let bgContentLabelActive = false;

    this.rewardsExpProgressNode.active = false;

    // 任务失败不显示 this.missionFailedTitleBg
    this.node.getChildByPath("Bg/Title/Bg").getComponent(Sprite).spriteFrame =
      desp === "Mission Failed" ? null : this.congratulationsTitleBg;
    this.node.getChildByPath("Bg/Title/Label").getComponent(Label).string =
      desp == "" ? "Mission Failed" : desp;

    if (GameData.mode === "fever") {
      this.node.getChildByPath("Bg/Title/Bg").getComponent(Sprite).spriteFrame =
        this.congratulationsTitleBg;
      this.node.getChildByPath("Bg/Title/Label").getComponent(Label).string =
        "Time's Up";
    }

    let thisDigProduct = this.digProductsArray[0];
    this.node
      .getChildByPath("Bg/Content/Label")
      .getComponent(Label).string = `+${thisDigProduct.count}`;
    let rewardNode = this.node.getChildByPath("Bg/Content/Reward");
    rewardNode.removeAllChildren();

    let tweenScaleTo = new Vec3(2, 2, 2);
    // 图标
    if (thisDigProduct.kind === "cardPart") {
    } else if (thisDigProduct.kind === "card") {
    } else {
      // 实际只会有coin、diamond、key、exp
      rewardNode.getComponent(Sprite).spriteFrame =
        this.CommonAssets[`levelReward${thisDigProduct.kind}IconFrame`];
      if (thisDigProduct.kind === "coin") {
        rewardNode.getComponent(Sprite).spriteFrame =
          this.CommonAssets.levelRewardMultiCoinsIconFrame;
        EventBus.emit("playSound", "settleCoin");
      } else if (thisDigProduct.kind === "diamond") {
        EventBus.emit("playSound", "settleOther");
      } else if (thisDigProduct.kind === "key") {
        EventBus.emit("playSound", "settleOther");
      }
      rewardNode.setScale(new Vec3(6, 6, 6));
      bgContentLabelActive = true;

      this.node
        .getChildByPath("Bg/Content/Label")
        .getComponent(Label).string = `Discovery +${thisDigProduct.count}`;
    }

    tween(rewardNode)
      .to(
        0.3,
        {
          scale: tweenScaleTo,
        },
        {
          onComplete: () => {
            if (
              UserData.currentLevelCallLadderCardID !== -1 ||
              UserData.currentLevelLampHalfCardID !== -1
            ) {
              this.node.getChildByPath("Bg/Content/Label").active =
                bgContentLabelActive;

              let startValue = thisDigProduct.count;
              let endValue = 0;
              let duration = 1; // 动画持续时间（秒）
              // 使用了“呼叫梯子”卡牌
              if (UserData.currentLevelCallLadderCardID !== -1) {
                endValue = Math.floor(
                  thisDigProduct.count *
                    (1 -
                      GameConfig.cards[UserData.currentLevelCallLadderCardID]
                        .value1)
                );
              }

              // 当前关卡使用了本局矿灯时间减半，但是获取的收益会翻倍
              if (UserData.currentLevelLampHalfCardID !== -1) {
                endValue = Math.floor(
                  thisDigProduct.count *
                    GameConfig.cards[UserData.currentLevelLampHalfCardID].value1
                );
              }

              let counter = { value: startValue };
              tween(counter)
                .to(
                  duration,
                  { value: endValue },
                  {
                    easing: "sineOut", // 使用平滑的缓动函数
                    onUpdate: () => {
                      // 更新 Label 文本，保留整数
                      this.node
                        .getChildByPath("Bg/Content/Label")
                        .getComponent(Label).string = `Discovery +${Math.floor(
                        counter.value
                      )}`;
                    },
                    onComplete: () => {
                      this.node.getChildByPath("Btn").active = true;
                      this.BackBtnNode.getChildByName("Label").getComponent(
                        Label
                      ).string =
                        this.digProductsArray.length === 1 ? "Done" : "Next";
                      this.BackBtnNode.parent.getComponent(
                        UITransform
                      ).priority = GameConfig.UIPriority.lampMask + 1;
                      this.isBackBtnClick = false;
                      this.BackBtnNode.on(
                        Input.EventType.TOUCH_END,
                        () => {
                          this.onCircleDigBackBtnClick(desp);
                        },
                        this
                      );

                      if (thisDigProduct.kind === "coin") {
                        // 显示看广告双倍金币按钮
                        this.AdNode.active = true;
                        this.isAdBtnClick = false;

                        this.AdNode.on(
                          Input.EventType.TOUCH_END,
                          () => {
                            this.onAdBtnClick(thisDigProduct.count);
                          },
                          this
                        );
                      }
                    },
                  }
                )
                .start();
            } else {
              this.node.getChildByPath("Btn").active = true;
              this.node.getChildByPath("Bg/Content/Label").active =
                bgContentLabelActive;
              this.BackBtnNode.getChildByName("Label").getComponent(
                Label
              ).string = this.digProductsArray.length === 1 ? "Done" : "Next";
              this.BackBtnNode.parent.getComponent(UITransform).priority =
                GameConfig.UIPriority.lampMask + 1;
              this.isBackBtnClick = false;
              this.BackBtnNode.on(
                Input.EventType.TOUCH_END,
                () => {
                  this.onCircleDigBackBtnClick(desp);
                },
                this
              );
              if (thisDigProduct.kind === "coin") {
                // 显示看广告双倍金币按钮
                this.AdNode.active = true;
                this.isAdBtnClick = false;

                this.AdNode.on(
                  Input.EventType.TOUCH_END,
                  () => {
                    this.onAdBtnClick(thisDigProduct.count);
                  },
                  this
                );
              }
            }

            // 如果是经验值，显示经验条动画
            if (thisDigProduct.kind === "exp") {
              this.showRewardsExpProgress(thisDigProduct.count);
            }
          },
        }
      )
      .start();
  }

  private onCircleRewardBackBtnClick() {
    if (this.isBackBtnClick) {
      return;
    }
    this.isBackBtnClick = true;
    this.BackBtnNode.off(
      Input.EventType.TOUCH_END,
      this.onCircleRewardBackBtnClick,
      this
    );
    btnClick(this.BackBtnNode);

    this.rewardsArray.shift();
    if (this.rewardsArray.length > 0) {
      this.showRewards();
    } else {
      if (this.digProductsArray.length > 0) {
        // 如果还有自己挖的物品
        this.showDigProducts("Congratulations");
      } else {
        this.isBackBtnClick = false;
        this.onBackBtnClick();
      }
    }
    this.node.getChildByPath("Btn").active = false;
  }

  private onCircleDigBackBtnClick(desp: string = "") {
    if (this.isBackBtnClick) {
      return;
    }
    this.isBackBtnClick = true;
    this.BackBtnNode.off(
      Input.EventType.TOUCH_END,
      this.onCircleDigBackBtnClick,
      this
    );
    btnClick(this.BackBtnNode);

    this.digProductsArray.shift();
    if (this.digProductsArray.length > 0) {
      this.showDigProducts(desp);
    } else {
      this.isBackBtnClick = false;
      this.onBackBtnClick();
    }
    this.node.getChildByPath("Btn").active = false;
  }

  private runPackItemToEnd() {
    const backpackNode = this.node.getChildByName("Backpack");

    const list = backpackNode.children[0].children;
    const list2 = backpackNode.children[1].children;
    const backpackList = [...list, ...list2];
    for (let i = 0; i < backpackList.length; i++) {
      const item = backpackList[i];
      const com = item.getComponent(BcakItem);
      if (item.active && com.count > 0 && com.oreName != "") {
        com.endCount = Math.floor(com.count);
      }
    }
  }

  private runPackItemToAdd() {
    const backpackNode = this.node.getChildByName("Backpack");

    const list = backpackNode.children[0].children;
    const list2 = backpackNode.children[1].children;

    const backpackList = [...list, ...list2];
    for (let i = 0; i < backpackList.length; i++) {
      const item = backpackList[i];
      const pos = item.position;
      const com = item.getComponent(BcakItem);
      if (item.active && com.count > 0 && com.oreName != "") {
        com.endCount = Math.floor(com.count * 2);
        const nod = instantiate(this.backpackItem);
        const itemS = item
          .getChildByName("Sprite")
          .getComponent(Sprite).spriteFrame;
        nod.getChildByName("Sprite").getComponent(Sprite).spriteFrame = itemS;
        nod.getChildByName("Sprite").getComponent(UITransform).width = 60;
        nod.getChildByName("Sprite").getComponent(UITransform).height = 60;
        nod.setPosition(pos.x, pos.y + 50, 0);

        this.node.addChild(nod);
        tween(nod)
          .to(
            0.5 + i * 0.1,
            {
              position: new Vec3(pos.x, pos.y, 0),
            },
            {
              easing: "linear",
              onComplete: () => {
                const comp = item.getComponent(BcakItem);
                item.getChildByName("Label").getComponent(Label).string =
                  Math.floor(comp.count * 2).toString();

                EventBus.emit("playSound", "coin");
                nod.destroy();
              },
            }
          )
          .start();
      }
    }
  }

  public runPackItemToWarehouse() {
    const backpackNode = this.node.getChildByName("Backpack");

    const list = backpackNode.children[0].children;
    const list2 = backpackNode.children[1].children;

    const backpackList = [...list, ...list2];
    for (let i = 0; i < backpackList.length; i++) {
      const item = backpackList[i];
      const pos = item.position;
      const com = item.getComponent(BcakItem);
      if (item.active && com.count > 0 && com.oreName != "") {
        com.endCount = Math.floor(com.count / 2);
        const nod = instantiate(this.backpackItem);
        const itemS = item
          .getChildByName("Sprite")
          .getComponent(Sprite).spriteFrame;
        nod.getChildByName("Sprite").getComponent(Sprite).spriteFrame = itemS;
        nod.getChildByName("Sprite").getComponent(UITransform).width = 60;
        nod.getChildByName("Sprite").getComponent(UITransform).height = 60;
        nod.position = pos;

        this.node.addChild(nod);
        tween(nod)
          .to(
            0.5 + i * 0.1,
            {
              position: new Vec3(pos.x, pos.y + 50, 0),
            },
            {
              easing: "linear",
              onComplete: () => {
                const comp = item.getComponent(BcakItem);
                item.getChildByName("Label").getComponent(Label).string =
                  Math.floor(comp.count / 2).toString();

                EventBus.emit("playSound", "coin");
                nod.destroy();
              },
            }
          )
          .start();
      }
    }
  }
}
