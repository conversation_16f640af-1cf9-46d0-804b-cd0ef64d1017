import { _decorator, Component, Node, Collider2D, Contact2DType, IPhysics2DContact, tween, Vec3, RigidBody2D, PhysicsSystem2D, director } from 'cc';
import { Player } from '../Player';
import { EventBus } from '../EventBus';
const { ccclass, property } = _decorator;

@ccclass('TreasureBox')
export class TreasureBox extends Component {

    @property({
        type: Node,
        tooltip: '宝箱打开时显示的节点'
    })
    public openSprite: Node = null;

    @property({
        type: Node,
        tooltip: '宝箱关闭时显示的节点'
    })
    public closedSprite: Node = null;

    public isOpened: boolean = false;
    private collider: Collider2D = null;
    private rigidBody: RigidBody2D = null;
    private playerNode: Node = null;
    private interactionDistance: number = 100; // 玩家与宝箱的交互距离
    public treasureContents: any[] = []; // 宝箱内容配置

    start() {
        // 自动查找子节点
        if (!this.openSprite) {
            this.openSprite = this.node.getChildByName('Open');
        }
        if (!this.closedSprite) {
            this.closedSprite = this.node.getChildByName('Sprite');
        }

        // 获取刚体组件并确保启用碰撞监听
        this.rigidBody = this.node.getComponent(RigidBody2D);
        if (this.rigidBody) {
            this.rigidBody.enabledContactListener = true;
            console.log('TreasureBox: 已启用刚体碰撞监听', this.rigidBody.enabledContactListener);
        } else {
            console.warn('TreasureBox: 找不到刚体组件');
        }

        // 获取碰撞器组件
        this.collider = this.node.getComponent(Collider2D);
        if (this.collider) {
            // 启用碰撞监听
            this.collider.on(Contact2DType.BEGIN_CONTACT, this.onBeginContact, this);
            console.log('TreasureBox: 已注册碰撞事件监听');
        } else {
            console.warn('TreasureBox: 找不到碰撞器组件');
        }

        // 确保初始状态正确
        this.setTreasureState(false);

        // 打印碰撞组信息以便调试
        if (this.collider) {
            console.log('TreasureBox: 碰撞器组别:', this.collider.group);
            // 设置碰撞掩码，确保能与所有组碰撞（特别是玩家）
            // 这里设置为能与所有组碰撞，如果需要更精确的控制，可以调整这个值
            console.log('TreasureBox: 碰撞器掩码:', '默认设置');
        }
        if (this.rigidBody) {
            console.log('TreasureBox: 刚体组别:', this.rigidBody.group, '碰撞监听已启用:', this.rigidBody.enabledContactListener);
        }

        // 检查物理系统状态
        const physicsSystem = PhysicsSystem2D.instance;
        console.log('TreasureBox: 物理系统已启用:', physicsSystem.enable);

        // 尝试自动查找玩家节点
        this.tryFindPlayer();

        console.log('TreasureBox: 初始化完成');
    }

    /**
     * 碰撞开始时的回调
     */
    private onBeginContact(_selfCollider: Collider2D, otherCollider: Collider2D, _contact: IPhysics2DContact | null) {
        // 详细的碰撞调试信息
        console.log('=== TreasureBox 碰撞事件 ===');
        console.log('碰撞对象节点名称:', otherCollider.node.name);
        console.log('碰撞对象组别:', otherCollider.group);
        console.log('宝箱组别:', _selfCollider.group);
        console.log('碰撞对象组件:', otherCollider.node.components.map(c => c.constructor.name));

        // 检查碰撞的对象是否是玩家
        const otherNode = otherCollider.node;
        const playerComponent = otherNode.getComponent(Player);
        console.log('是否为玩家:', !!playerComponent);
        console.log('宝箱是否已打开:', this.isOpened);

        if (playerComponent && !this.isOpened) {
            console.log('触发宝箱打开!');
            // 玩家碰到宝箱，打开宝箱
            this.openTreasure();
        } else {
            console.log('不满足打开条件');
        }
        console.log('=== 碰撞事件结束 ===');
    }

    /**
     * 打开宝箱
     */
    private openTreasure() {
        if (this.isOpened) return;

        this.isOpened = true;
        this.setTreasureState(true);

        // 播放音效
        EventBus.emit('playSound', 'success');

        // 添加打开动画效果
        if (this.openSprite) {
            // 让打开的sprite从小到大弹出
            this.openSprite.setScale(new Vec3(0, 0, 1));
            tween(this.openSprite)
                .to(0.3, { scale: new Vec3(1, 1, 1) }, {
                    easing: 'backOut'
                })
                .start();
        }

        // 可以添加奖励逻辑
        this.giveReward();
    }

    /**
     * 设置宝箱状态
     */
    public setTreasureState(opened: boolean) {
        if (this.openSprite) {
            this.openSprite.active = opened;
        }
        if (this.closedSprite) {
            this.closedSprite.active = !opened;
        }
    }

    /**
     * 给予奖励
     */
    /**
     * 给予奖励，支持延时播放动画
     */
    private giveReward() {
        console.log('宝箱已打开，获得奖励！');

        // 如果有配置的宝箱内容，使用配置的奖励
        if (this.treasureContents && this.treasureContents.length > 0) {
            // 延时发送每个奖励事件，让动画依次播放
            for (let i = 0; i < this.treasureContents.length; i++) {
                const treasure = this.treasureContents[i];
                const delay = i * 0.3; // 每个奖励间隔0.3秒

                this.scheduleOnce(() => {
                    // 通过事件系统通知其他组件，传递宝箱节点用于准确获取起始位置
                    EventBus.emit('treasureOpened', {
                        type: treasure.type,
                        amount: treasure.amount,
                        id: treasure.id,
                        position: this.node.position,
                        node: this.node // 传递宝箱节点
                    });

                    console.log(`获得奖励: ${treasure.type} x${treasure.amount}${treasure.id ? ` (ID: ${treasure.id})` : ''}`);
                }, delay);
            }
        } else {
            // 使用默认奖励
            EventBus.emit('treasureOpened', {
                type: 'diamond',
                amount: 100,
                position: this.node.position,
                node: this.node // 传递宝箱节点
            });

            console.log('使用默认奖励: diamond x100');
        }
    }

    /**
     * 尝试自动查找玩家节点
     */
    private tryFindPlayer(): void {
        // 先在TiledMap中查找
        const tiledMap = director.getScene().getChildByPath('Canvas/TiledMap');
        if (tiledMap) {
            this.playerNode = tiledMap.getChildByName('Player');
        }

        // 如果还是没找到，直接在Canvas下查找
        if (!this.playerNode) {
            this.playerNode = director.getScene().getChildByPath('Canvas/Player');
        }

        if (!this.playerNode) {
            console.warn('TreasureBox: 无法自动找到玩家节点');
        } else {
            console.log('TreasureBox: 找到玩家节点:', this.playerNode.name);
        }
    }

    /**
     * 每帧检测玩家距离（作为碰撞检测的备用方案）
     */
    update(_deltaTime: number) {
        // 如果宝箱已经打开，不需要继续检测
        if (this.isOpened || !this.playerNode) return;

        // 获取玩家和宝箱的世界坐标
        let playerWorldPos = new Vec3();
        let treasureWorldPos = new Vec3();
        this.playerNode.getWorldPosition(playerWorldPos);
        this.node.getWorldPosition(treasureWorldPos);

        // 计算距离
        let distance = Vec3.distance(playerWorldPos, treasureWorldPos);

        // 如果玩家在交互范围内，打开宝箱
        if (distance <= this.interactionDistance) {
            console.log('TreasureBox: 通过距离检测触发宝箱打开! 距离:', distance);
            this.openTreasure();
        }
    }

    onDestroy() {
        // 清理碰撞监听
        if (this.collider) {
            this.collider.off(Contact2DType.BEGIN_CONTACT, this.onBeginContact, this);
        }
    }

    /**
     * 公开方法：手动打开宝箱
     */
    public open() {
        this.openTreasure();
    }

    /**
     * 公开方法：重置宝箱状态
     */
    public reset() {
        this.isOpened = false;
        this.setTreasureState(false);
    }

    /**
     * 获取宝箱是否已打开
     */
    public get opened(): boolean {
        return this.isOpened;
    }

    /**
     * 设置宝箱内容
     */
    public setTreasureContents(contents: any[]) {
        this.treasureContents = contents || [];
        console.log('TreasureBox: 设置宝箱内容', this.treasureContents);
    }
}
