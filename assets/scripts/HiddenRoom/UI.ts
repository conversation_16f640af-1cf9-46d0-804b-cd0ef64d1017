import { Label } from 'cc';
import { _decorator, Component, Node } from 'cc';
import { UserData } from '../Utils/UserData';
import { Player } from '../Player';
import { director } from 'cc';
const { ccclass } = _decorator;

@ccclass('UI')
export class UI extends Component {
    private dirtNumNode: Node;
    private diamondNumNode: Node;
    private keysNumNode: Node;
    private player: Player;

    start() {
        this.dirtNumNode = this.node.getChildByPath('Dirt/Label');
        this.diamondNumNode = this.node.getChildByPath('Diamond/Label');
        this.keysNumNode = this.node.getChildByPath('Keys/Label');

        this.player = director
            .getScene()
            .getChildByPath('Canvas/TiledMap/Player')
            ?.getComponent(Player);
    }

    update(_deltaTime: number) {
        if (this.player) {
            this.dirtNumNode.getComponent(Label).string = this.player.currentDirt.toString();
        }
        this.diamondNumNode.getComponent(Label).string =
            UserData.currentLevelDiamondCount.toString();
        this.keysNumNode.getComponent(Label).string = UserData.currentLevelKeyCount.toString();
    }
}
