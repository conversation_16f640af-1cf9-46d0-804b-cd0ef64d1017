import {
    _decorator,
    Component,
    Node,
    tween,
    Vec3,
    Label,
    Color,
    sys,
    input,
    Input,
    KeyC<PERSON>,
    director,
} from 'cc';
import { EventBus } from '../EventBus';
const { ccclass, property } = _decorator;

// Exit -> {Flash, Door , GamePad -> {Direct}}

@ccclass('ExitDoor')
export class ExitDoor extends Component {
    public playerNode: Node = null;

    @property({ tooltip: '是入口还是出口' })
    public isExit: boolean = true;

    private flashNode: Node = null;
    private directNode: Node = null;
    private tipNode: Node = null;
    private isWKeyPressed: boolean = false;
    private isPlayerNearby: boolean = false;
    private interactionDistance: number = 150; // 玩家与出口的交互距离

    start() {
        // 如果没有设置玩家节点，尝试自动查找
        if (!this.playerNode) {
            console.log('ExitDoor: 玩家节点未设置，尝试自动查找');
            this.tryFindPlayer();
        }
        this.initializeNodes();
        this.startAnimations();

        // 根据showTip属性决定是否创建提示节点
        if (this.isExit) {
            this.createTipNode();
        } else {
            this.createUiTipNode();
        }

        this.setupInputListeners();
        console.log('ExitDoor: 初始化完成');
    }

    private initializeNodes() {
        // 查找Flash节点
        this.flashNode = this.node.getChildByName('Flash');
        if (!this.flashNode) {
            console.warn('ExitDoor: 找不到Flash节点');
        }

        // 查找GamePad中的Direct节点
        const gamePadNode = this.node.getChildByName('GamePad');
        if (gamePadNode) {
            this.directNode = gamePadNode.getChildByName('Direct');
            if (!this.directNode) {
                console.warn('ExitDoor: 找不到GamePad中的Direct节点');
            }
        } else {
            console.warn('ExitDoor: 找不到GamePad节点');
        }
    }

    private startAnimations() {
        this.startFlashRotation();
        this.startDirectBlink();
    }

    private startFlashRotation() {
        if (!this.flashNode) return;

        tween(this.flashNode)
            .by(4.0, { eulerAngles: new Vec3(0, 0, 360) }) // 4秒旋转360度（原来2秒）
            .repeatForever() // 无限循环
            .start();
    }

    private startDirectBlink() {
        if (!this.directNode) return;

        // 使用active属性来控制显示隐藏，更直接高效
        const blinkInterval = () => {
            this.directNode.active = !this.directNode.active;
        };

        // 每0.5秒切换一次active状态
        this.schedule(blinkInterval, 0.5);
    }

    //在UI上创建提示
    private createUiTipNode() {
        this.tipNode = new Node('ExitTip');
        const label = this.tipNode.addComponent(Label);

        // 根据系统判断显示的文本，手机设备显示"点击上键返回"
        if (sys.isMobile) {
            label.string = '点击上键进入密室';
        } else {
            label.string = '按W或上键进入密室';
        }

        label.fontSize = 24;
        label.color = Color.WHITE;
        this.playerNode.addChild(this.tipNode);
        this.tipNode.setPosition(new Vec3(0, 100, 0));

        // 默认隐藏提示
        this.tipNode.active = false;
    }

    /**
     * 创建提示文本节点
     */
    private createTipNode() {
        // 创建提示节点，默认隐藏
        this.tipNode = new Node('ExitTip');
        const label = this.tipNode.addComponent(Label);

        // 根据系统判断显示的文本，手机设备显示"点击上键返回"
        if (sys.isMobile) {
            label.string = '点击上键返回';
        } else {
            label.string = '按W或上键返回';
        }

        label.fontSize = 24;
        label.color = Color.WHITE;

        // 将提示节点添加为出口门的子节点，并定位到上方
        this.node.addChild(this.tipNode);
        this.tipNode.setPosition(new Vec3(0, 100, 0));

        // 默认隐藏提示
        this.tipNode.active = false;
    }

    /**
     * 显示出口提示
     */
    public showExitTip() {
        if (this.tipNode) {
            this.tipNode.active = true;

            // 添加轻微上下浮动动画
            this.unschedule(this.animateTip);
            this.schedule(this.animateTip, 0.05);

            // 让门的闪光旋转更快，表示可以交互
            if (this.flashNode) {
                // 移除之前的动画并重新创建一个更快的旋转
                tween(this.flashNode).stop(); // 停止当前Tween
                tween(this.flashNode)
                    .by(2.0, { eulerAngles: new Vec3(0, 0, 360) }) // 加速旋转
                    .repeatForever()
                    .start();
            }

            // GamePad闪烁更快
            if (this.directNode) {
                this.unscheduleAllCallbacks();
                const blinkFaster = () => {
                    this.directNode.active = !this.directNode.active;
                };
                this.schedule(blinkFaster, 0.25); // 更快的闪烁
            }
        }
    }

    /**
     * 隐藏出口提示
     */
    public hideExitTip() {
        if (this.tipNode) {
            this.tipNode.active = false;
            this.unschedule(this.animateTip);

            // 恢复正常旋转速度
            if (this.flashNode) {
                // 移除之前的动画并重新创建一个更快的旋转
                tween(this.flashNode).stop(); // 停止当前Tween
                tween(this.flashNode)
                    .by(4.0, { eulerAngles: new Vec3(0, 0, 360) }) // 恢复正常速度
                    .repeatForever()
                    .start();
            }

            // 恢复正常闪烁速度
            if (this.directNode) {
                this.unscheduleAllCallbacks();
                const blinkNormal = () => {
                    this.directNode.active = !this.directNode.active;
                };
                this.schedule(blinkNormal, 0.5); // 正常闪烁
            }
        }
    }

    /**
     * 提示文本浮动动画
     */
    private animateTip() {
        if (!this.tipNode) return;

        const time = Date.now() / 1000;
        const offsetY = Math.sin(time * 3) * 5; // 使用正弦波实现上下浮动效果
        this.tipNode.setPosition(new Vec3(0, 100 + offsetY, 0));
    }

    /**
     * 尝试自动查找玩家节点
     */
    private tryFindPlayer(): void {
        // 先在TiledMap中查找
        const tiledMap = director.getScene().getChildByPath('Canvas/TiledMap');
        if (tiledMap) {
            this.playerNode = tiledMap.getChildByName('Player');
        }

        // 如果还是没找到，直接在Canvas下查找
        if (!this.playerNode) {
            this.playerNode = director.getScene().getChildByPath('Canvas/Player');
        }

        if (!this.playerNode) {
            console.warn('ExitDoor: 无法自动找到玩家节点，请手动设置');
        }
    }

    /**
     * 设置输入监听
     */
    private setupInputListeners(): void {
        // 设置键盘监听
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        input.on(Input.EventType.KEY_UP, this.onKeyUp, this);
    }

    /**
     * 键盘按下处理
     */
    private onKeyDown(event: any): void {
        // 检测W键按下
        if (event.keyCode === KeyCode.KEY_W) {
            this.isWKeyPressed = true;
            this.checkForExit();
        }
    }

    /**
     * 键盘抬起处理
     */
    private onKeyUp(event: any): void {
        // 检测W键抬起
        if (event.keyCode === KeyCode.KEY_W) {
            this.isWKeyPressed = false;
        }
    }

    /**
     * 检测上键是否被按下（W键或GamePad上键）
     */
    private isUpKeyPressed(): boolean {
        // 检查键盘W键是否被按下
        if (this.isWKeyPressed) {
            return true;
        }

        // 检查GamePad是否存在且上键方向是否激活
        const gamepad = director.getScene().getChildByPath('Canvas/GamePad');
        if (gamepad) {
            // 检查Direct节点的角度是否为90度（上方向）且active为true
            const directNode = gamepad.getChildByPath('Direct');
            if (directNode && directNode.active && directNode.angle === 90) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查玩家与出口的交互
     */
    private checkForExit(): void {
        if (!this.playerNode) return;

        // 获取玩家和Exit的世界坐标
        let playerWorldPos = new Vec3();
        let exitWorldPos = new Vec3();
        this.playerNode.getWorldPosition(playerWorldPos);
        this.node.getWorldPosition(exitWorldPos);

        // 计算距离
        let distance = Vec3.distance(playerWorldPos, exitWorldPos);

        // 判断是否在交互范围内并且按下了上键
        if (distance <= this.interactionDistance && this.isUpKeyPressed()) {
            this.activateExit();
        }

        // 更新提示显示/隐藏状态
        this.isPlayerNearby = distance <= this.interactionDistance;
    }

    /**
     * 激活交互，根据isExit属性决定是进入隐藏房间还是返回主场景
     */
    private activateExit(): void {
        // 播放音效
        EventBus.emit('playSound', 'success');

        if (this.isExit) {
            // 出口：返回主场景
            director.loadScene('game');
        } else {
            // 入口：进入隐藏房间
            director.loadScene('hiddenRoom');
        }
    }

    onDestroy() {
        // 移除键盘监听
        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        input.off(Input.EventType.KEY_UP, this.onKeyUp, this);

        // 清理定时器，避免内存泄露
        this.unscheduleAllCallbacks();
    }

    update(_deltaTime: number) {
        // 检测玩家是否靠近和交互
        if (this.playerNode) {
            // 获取玩家和Exit的世界坐标
            let playerWorldPos = new Vec3();
            let exitWorldPos = new Vec3();
            this.playerNode.getWorldPosition(playerWorldPos);
            this.node.getWorldPosition(exitWorldPos);

            // 计算距离
            let distance = Vec3.distance(playerWorldPos, exitWorldPos);

            // 更新提示显示/隐藏状态
            const wasNearby = this.isPlayerNearby;
            this.isPlayerNearby = distance <= this.interactionDistance;

            // 当玩家状态变化时更新提示
            if (wasNearby !== this.isPlayerNearby) {
                if (this.isPlayerNearby) {
                    this.showExitTip();
                } else {
                    this.hideExitTip();
                }
            }

            // 检查按键状态
            if (this.isPlayerNearby && this.isUpKeyPressed()) {
                this.activateExit();
            }
        }
    }
}
