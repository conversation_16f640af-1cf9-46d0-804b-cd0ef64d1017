import { MapUtils } from './Utils/MapUtils';
import {
    _decorator,
    Color,
    Component,
    director,
    EventTouch,
    Graphics,
    Input,
    instantiate,
    Label,
    math,
    Node,
    Size,
    tween,
    UITransform,
    Vec3,
    view,
} from 'cc';
import { Bomb } from './Bomb';
import { EventBus } from './EventBus';
import { GameConfig } from './GameConfig';
import { GameData } from './GameData';
import { Map } from './Map';
import { Mine } from './Mine';
import { Player } from './Player';
import { StartBtn } from './Store/StartBtn';
import { OneCard } from './UI/OneCard';
import { UserData } from './Utils/UserData';
import { PlayerBuffCtl } from './Game/PlayerBuffCtl';
import { GameManager } from './GameManager';
import { Sprite } from 'cc';
import { ItemFloatUpCtl } from './Game/ItemFloatUpCtl';
import { CommonAssetsCtl } from './v1/Common/CommonAssetsCtl';
import { HiddenMap } from './HiddenRoom/HiddenMap';
import { ExitDoor } from './HiddenRoom/ExitDoor';
const { ccclass } = _decorator;

@ccclass('Item')
export class Item extends Component {
    private commonAssets: CommonAssetsCtl;
    private playerS: Player;
    private uiNode: Node;
    private map: Map | HiddenMap;
    private cardsPopupNode: Node;
    private gameManager: GameManager;
    private winSize: Size;

    private usedCardIDs = []; // 记录已经使用过的cardID
    private currentDrawFreeCardID = null; // 当前抽哪一个卡是免费的

    start() {
        this.commonAssets = director
            .getScene()
            .getChildByPath('Canvas/CommonAssets')
            .getComponent(CommonAssetsCtl);
        this.playerS = this.node.getComponent(Player) as Player;
        this.uiNode = director.getScene().getChildByPath('Canvas/UI');
        this.map = director.getScene().getChildByPath('Canvas/TiledMap').getComponent(Map)
            ? director.getScene().getChildByPath('Canvas/TiledMap').getComponent(Map)
            : director.getScene().getChildByPath('Canvas/TiledMap').getComponent(HiddenMap);
        this.gameManager = director
            .getScene()
            .getChildByPath('Canvas/GameManager')
            .getComponent(GameManager);
        this.winSize = view.getVisibleSize();

        this.cardsPopupNode = director.getScene().getChildByPath('Canvas/UI/CardsPopup');
        // 拦截底层点击事件
        if (this.cardsPopupNode) {
            this.cardsPopupNode.on(Input.EventType.TOUCH_START, (event: EventTouch) => {
                event.propagationStopped = true;
            });
        }
    }

    public plantBomb(kind: number): Node {
        let bomb;
        if (kind == GameConfig.blockKinds.BOMB_ZONE) {
            bomb = instantiate(this.playerS.map.bombFab);
            bomb.setPosition(this.node.position.clone().add(new Vec3(0, 0, 0)));
        } else if (kind == GameConfig.blockKinds.BOMB_FLYING) {
            bomb = instantiate(this.playerS.map.bombFlyingFab);
            bomb.setPosition(this.node.position.clone().add(new Vec3(0, 10, 0)));
            bomb.getComponent(Bomb).timeout = 1;
            bomb.getComponent(Bomb).state = GameConfig.mineState.IN_DIRT; // 初始状态不启动倒计时
        }
        if (kind == GameConfig.blockKinds.BOMB_GRENADE) {
            bomb = instantiate(this.playerS.map.bombGrenadeFab);
            bomb.setPosition(this.node.position.clone().add(new Vec3(0, 22, 0)));
        }
        EventBus.emit('playSound', 'useItem');
        bomb.getComponent(Bomb).kind = kind;
        this.playerS.map.node.addChild(bomb);
        return bomb;
    }

    public plantHiddenRoom(): Node {
        if (this.playerS.map instanceof Map) {
            const hiddenRoom = instantiate(this.playerS.map.hiddenRoomEntranceFab);
            hiddenRoom.getComponent(ExitDoor).isExit = false;
            hiddenRoom.setScale(new Vec3(0.25, 0.25, 1));
            hiddenRoom.setPosition(this.node.position.clone().add(new Vec3(0, 0, 0)));
            this.map.node.addChild(hiddenRoom);
            return hiddenRoom;
        }

        return null;
    }

    public useLadder() {
        // EventBus.emit("playSound", "useItem");
        let ladderGID = MapUtils.getNearGIDByLocation(this.node.position, 'left', this.map.mapSize);
        let dir: string;
        // 左侧留一格
        if (ladderGID.x > 3) {
            dir = 'left';
        } else {
            ladderGID = MapUtils.getNearGIDByLocation(
                this.node.position,
                'right',
                this.map.mapSize
            );
            dir = 'right';
        }
        // 梯子一直放满这个屏幕，视觉效果更好
        let max = 20;
        for (let y = 0; y <= ladderGID.y; y++) {
            if (y > max) break;
            let gid = new Vec3(ladderGID.x, ladderGID.y - y, 0);

            this.playerS.dig(gid, 999); // 放梯子强制破转，更有意思
        }
        this.playerS.enterExit(true, 1, dir);
    }

    public hiddenRoomPickupItem(b: Node, bs: Mine, count?: number) {
        if (bs.subKind == GameConfig.blockKinds.ITEM_HEART) {
            EventBus.emit('playSound', 'pickupItem');
            this.playerS.hp += GameData.currentLevelConfig.itemHeart;
            // 向上飘的效果
            let itemFloatUp = instantiate(this.map.itemFloatUpPrefab);
            itemFloatUp.getComponent(ItemFloatUpCtl).kind = 'Heart';
            itemFloatUp.getComponent(ItemFloatUpCtl).count = GameData.currentLevelConfig.itemHeart;
            this.map.player.addChild(itemFloatUp);
        } else if (bs.subKind == GameConfig.blockKinds.ITEM_LAMP) {
            EventBus.emit('playSound', 'pickupItem');
            this.playerS.lampPower += GameData.currentLevelConfig.itemLamp;
            // 向上飘的效果
            let itemFloatUp = instantiate(this.map.itemFloatUpPrefab);
            itemFloatUp.getComponent(ItemFloatUpCtl).kind = 'Lamp';
            itemFloatUp.getComponent(ItemFloatUpCtl).count = GameData.currentLevelConfig.itemLamp;
            this.map.player.addChild(itemFloatUp);
        } else if (bs.subKind == GameConfig.blockKinds.ITEM_HIDDEN_MONEY) {
            EventBus.emit('playSound', 'pickupItem');
            // this.uiNode.getComponent(UI).updateMoney(GameData.currentLevelConfig.itemHiddenMoney);

            EventBus.emit('coinAdd', count, b, this.map.player);
        } else if (bs.subKind == GameConfig.blockKinds.ITEM_KEY) {

            EventBus.emit('playSound', 'pickupChest');
            // 获取钥匙

            UserData.currentLevelKeyCount += 1;
            // 钥匙飞到左上
            const keyS = instantiate(this.gameManager.backpackS);
            keyS.getChildByName('Sprite').getComponent(Sprite).spriteFrame =
                this.commonAssets.itemKeyFrame;
            const pos = this.map.convertChildPositionToUI(b);

            keyS.setPosition(pos);
            this.uiNode.addChild(keyS);
            const localParentPos = this.uiNode.getChildByPath('Keys').getPosition();
            tween(keyS)
                .parallel(
                    tween().to(1, { position: new Vec3(localParentPos.x, localParentPos.y, 0) }),
                    tween().to(1, { scale: new Vec3(0.5, 0.5, 1) })
                )
                .call(() => {
                    keyS.destroy();
                })
                .start();
        } else if (bs.subKind == GameConfig.blockKinds.ITEM_DIAMOND) {

            EventBus.emit('playSound', 'pickupChest');
            // 获取钻石

            UserData.currentLevelDiamondCount += 1;
            // 钻石飞到左上
            const diamondS = instantiate(this.gameManager.backpackS);
            diamondS.getChildByName('Sprite').getComponent(Sprite).spriteFrame =
                this.commonAssets.itemDiamondFrame;
            const pos = this.map.convertChildPositionToUI(b);

            diamondS.setPosition(pos);
            this.uiNode.addChild(diamondS);
            const localParentPos = this.uiNode.getChildByPath('Diamond').getPosition();
            tween(diamondS)
                .parallel(
                    tween().to(1, { position: new Vec3(localParentPos.x, localParentPos.y, 0) }),
                    tween().to(1, { scale: new Vec3(0.5, 0.5, 1) })
                )
                .call(() => {
                    diamondS.destroy();
                })
                .start();
        }
    }

    public pickupItem(b: Node, bs: Mine) {

        // 隐藏房间入口不能被拾取，直接返回
        if (bs.subKind == GameConfig.blockKinds.ITEM_HIDDEN_ROOM_ENTRANCE) {
            return;
        }

        if (bs.subKind == GameConfig.blockKinds.ITEM_HEART) {
            EventBus.emit('playSound', 'pickupItem');
            this.playerS.hp += GameData.currentLevelConfig.itemHeart;
            // 向上飘的效果
            let itemFloatUp = instantiate(this.map.itemFloatUpPrefab);
            itemFloatUp.getComponent(ItemFloatUpCtl).kind = 'Heart';
            itemFloatUp.getComponent(ItemFloatUpCtl).count = GameData.currentLevelConfig.itemHeart;
            this.map.player.addChild(itemFloatUp);
        } else if (bs.subKind == GameConfig.blockKinds.ITEM_LAMP) {
            EventBus.emit('playSound', 'pickupItem');
            this.playerS.lampPower += GameData.currentLevelConfig.itemLamp;
            // 向上飘的效果
            let itemFloatUp = instantiate(this.map.itemFloatUpPrefab);
            itemFloatUp.getComponent(ItemFloatUpCtl).kind = 'Lamp';
            itemFloatUp.getComponent(ItemFloatUpCtl).count = GameData.currentLevelConfig.itemLamp;
            this.map.player.addChild(itemFloatUp);
        } else if (bs.subKind == GameConfig.blockKinds.ITEM_HIDDEN_MONEY) {
            EventBus.emit('playSound', 'pickupItem');
            // this.uiNode.getComponent(UI).updateMoney(GameData.currentLevelConfig.itemHiddenMoney);
            EventBus.emit(
                'coinAdd',
                GameData.currentLevelConfig.itemHiddenMoney,
                b,
                this.map.player
            );
        } else if (bs.subKind == GameConfig.blockKinds.ITEM_KEY) {
            EventBus.emit('playSound', 'pickupChest');
            // 获取钥匙
            UserData.currentLevelKeyCount += 1;
            // 钥匙飞到左上
            const keyS = instantiate(this.gameManager.backpackS);
            keyS.getChildByName('Sprite').getComponent(Sprite).spriteFrame =
                this.commonAssets.itemKeyFrame;
            const pos = this.map.convertChildPositionToUI(b);
            keyS.setPosition(pos);
            this.uiNode.addChild(keyS);
            const localParentPos = this.uiNode.getChildByPath('Keys').getPosition();
            tween(keyS)
                .parallel(
                    tween().to(1, { position: new Vec3(localParentPos.x, localParentPos.y, 0) }),
                    tween().to(1, { scale: new Vec3(0.5, 0.5, 1) })
                )
                .call(() => {
                    keyS.destroy();
                })
                .start();
        } else if (bs.subKind == GameConfig.blockKinds.ITEM_DIAMOND) {
            EventBus.emit('playSound', 'pickupChest');
            // 获取钻石
            UserData.currentLevelDiamondCount += 1;
            // 钻石飞到左上
            const diamondS = instantiate(this.gameManager.backpackS);
            diamondS.getChildByName('Sprite').getComponent(Sprite).spriteFrame =
                this.commonAssets.itemDiamondFrame;
            const pos = this.map.convertChildPositionToUI(b);
            diamondS.setPosition(pos);
            this.uiNode.addChild(diamondS);
            const localParentPos = this.uiNode.getChildByPath('Diamond').getPosition();
            tween(diamondS)
                .parallel(
                    tween().to(1, { position: new Vec3(localParentPos.x, localParentPos.y, 0) }),
                    tween().to(1, { scale: new Vec3(0.5, 0.5, 1) })
                )
                .call(() => {
                    diamondS.destroy();
                })
                .start();
        }
    }

    public pickupMine(b: Node, bs: Mine) {
        // 经验值
        if (bs.kind == GameConfig.blockKinds.MINE_A19) {
            this.playerS.currentExp += 25; // 10
            EventBus.emit('playSound', 'pickupMine');

            // 删除经验矿石
            for (let i = 0; i < this.map.blocks.length; i++) {
                let block = this.map.blocks[i] as Node;
                if (block.uuid == b.uuid) {
                    this.map.blocks.splice(i, 1);
                    i++;
                    break;
                }
            }
            // 经验矿石向上飞
            const expB = instantiate(this.gameManager.backpackS);
            expB.getChildByName('Sprite').getComponent(Sprite).spriteFrame =
                this.gameManager.getSpriteFrame(bs.kind.toString());
            const pos = this.map.convertChildPositionToUI(b);

            expB.setPosition(pos);
            this.uiNode.addChild(expB);
            const localParentPos = this.uiNode.getChildByPath('Status').getPosition();
            const localPosX = localParentPos.x + 150; // +150ExpBar的x
            const localPosY = localParentPos.y - 300; // -300ExpBar的y
            tween(expB)
                .to(
                    0.5,
                    {
                        position: new Vec3(localPosX, localPosY, 0),
                    },
                    {
                        onComplete: () => {
                            expB.destroy();
                        },
                    }
                )
                .start();
            b.destroy();

            // 如果经验值满了，抽卡
            if (this.playerS.currentExp >= this.playerS.fullExp) {
                this.playerS.currentExp = 0;
                if (GameData.mode === 'general') {
                    this.openCardsPopup();
                }
            }
        } else {
            EventBus.emit('backpackAdd', b, { kind: bs.kind, subKind: bs.subKind });
            EventBus.emit('playSound', 'pickupMine');

            // 除了19号矿石，其他矿石直接兑换成金币
            EventBus.emit(
                'coinAdd',
                GameConfig.minePrice[bs.kind.toString()] * this.playerS.minesPriceRatio,
                b,
                this.map.player
            );

            // 如果任务中有矿石任务，飞到任务栏
            if (GameData.currentLevelConfig.targets.length > 0) {
                for (let i = 0; i < GameData.currentLevelConfig.targets.length; i++) {
                    let c = GameData.currentLevelConfig.targets[i];
                    if (c.kind == 'mine' && c.id == bs.kind) {

                        const mineS = instantiate(this.gameManager.backpackS);
                        mineS.getChildByName('Sprite').getComponent(Sprite).spriteFrame =
                            this.gameManager.getSpriteFrame(bs.kind.toString());
                        const pos = this.map.convertChildPositionToUI(b);

                        mineS.setPosition(pos);
                        this.uiNode.addChild(mineS);
                        const localParentPos = this.uiNode.getChildByPath('Status').getPosition();
                        const localPos = this.uiNode
                            .getChildByPath('Status/TargetArea')
                            .getPosition();
                        tween(mineS)
                            .to(
                                0.5,
                                {
                                    position: new Vec3(
                                        localParentPos.x + localPos.x,
                                        localParentPos.y + localPos.y,
                                        0
                                    ),
                                },
                                {
                                    onComplete: () => {
                                        mineS.destroy();
                                    },
                                }
                            )
                            .start();
                        break;
                    }
                }
            }
        }

        // 更新当前关卡的目标数据：mine矿石任务
        this.playerS.updateCurrentLevelTargetData('mine', 1, bs.kind);

        // this.uiNode.getComponent(UI).updateMoney(10);
    }

    openCardsPopup() {
        if (this.playerS.cannotControl()) return;
        GameConfig.currentGameState = GameConfig.gameStates.PAUSED;

        // 提高显示粘土层级
        director.getScene().getChildByPath('Canvas/UI/Dirt').getComponent(UITransform).priority =
            GameConfig.UIPriority.lampMask + 1;

        let title = this.cardsPopupNode.getChildByName('Title');
        title.getComponent(Label).string = 'Upgrade your skills';
        this.cardsPopupNode.active = true;

        this.drawCards();
    }

    // 重抽、广告重抽、全选
    drawnCardsRedrawBtn() {
        let container = this.cardsPopupNode.getChildByName('BtnsContainer');
        container.removeAllChildren();
        let b = instantiate(this.map.startBtnFab);
        let bs = b.getComponent(StartBtn);
        bs.delegate = this;
        if (GameData.cardsRedrawFreeCount > 0) {
            bs.opts.width = 220;
            bs.opts.height = 56;
            bs.opts.iconFrame = this.map.dirtFrame;
            bs.btnText = 'Refresh'; // 免费重抽
            // bs.onTouchStart = this.onLevelTouch;
            // bs.onTouchCancel = this.onLevelCancel;
            bs.onTouchEnd = () => {
                GameData.cardsRedrawFreeCount = GameData.cardsRedrawFreeCount - 1; // 减掉免费次数
                this.drawCards();
            };
        } else {
            // 免费抽卡使用一次后之后就必须看广告抽卡了
            bs.opts.width = 220;
            bs.opts.height = 56;
            bs.opts.iconFrame = this.map.adFrame;
            bs.btnText = 'Refresh'; //看广告重抽
            bs.onTouchEnd = () => {
                this.drawCards();

            };
        }
        b.setPosition(new Vec3(0, -this.winSize.height / 2 + 200, 0));
        container.addChild(b);

        // 创建随机全都要的广告按钮, 30%概率，最多出现两次
        let isShowGetAllBtn = math.random() < 0.3 ? true : false;
        if (isShowGetAllBtn && this.playerS.drawCardGetAllTimes > 0) {
            this.playerS.drawCardGetAllTimes--;

            let adBtnNode = instantiate(this.map.startBtnFab);
            let adBtnbs = adBtnNode.getComponent(StartBtn);
            adBtnbs.opts.width = 220;
            adBtnbs.opts.height = 86;
            adBtnbs.opts.iconFrame = this.map.adFrame;
            adBtnbs.btnText = 'Get All';
            adBtnbs.btnColor = new Color().fromHEX('#fcff00');
            adBtnbs.addiData = {
                cardID: 9999, // 全选
                isAd: true,
            };
            adBtnbs.delegate = this;
            adBtnbs.onTouchEnd = (self, event) => {

                this.onChooseCardBtnEnd(self, event);
            };
            adBtnNode.setPosition(new Vec3(0, -this.winSize.height / 2 + 400, 0));
            container.addChild(adBtnNode);
        }
    }

    drawCards() {
        EventBus.emit('playSound', 'cardPopup');
        // 随机从玩家的卡池里抽取3张卡片
        // 规则：
        // 1. 先把卡按照同类类型进行分组（同种类型会有多个星级）
        // 2. one_time类型的一局只能使用一次，之后不会再出现
        // 3. 抽到的卡如果有多个星级的，每个星级按照一定的概率抽取
        // 4. 如果玩家当前的粘土数量小于最终3个卡中最便宜的，那么把这个卡设为免费
        let cardGroups = {};
        for (let i = 0; i < GameData.cardsPool.length; i++) {
            let id = GameData.cardsPool[i];
            let obj = GameConfig.cards[id];

            let isUsed = false;
            if (obj.one_time) {
                for (let m = 0; m < this.usedCardIDs.length; m++) {
                    let usedObj = GameConfig.cards[this.usedCardIDs[m]];
                    if (usedObj.kind == obj.kind) {
                        isUsed = true;
                        break;
                    }
                }
            }

            if (!isUsed) {
                cardGroups[obj.kind] ||= [];
                cardGroups[obj.kind].push(obj);
            }
        }
        let cardObjects = Object.keys(cardGroups).map(key => cardGroups[key]); // 二维数组，每种类型一个数组
        let threeObjs = []; // 二维数组，最终选择三种
        for (let i = 0; i < 3; i++) {
            let idx = math.randomRangeInt(0, cardObjects.length);
            threeObjs.push(cardObjects[idx]);
            cardObjects.splice(idx, 1);
        }

        let cardIDs = [];
        let cardObjs = [];
        // 同种卡不同稀有度，持有的数量不同时每个卡的出现概率不一样
        // 比如，一种卡，只有稀有度1时，出现概率为100；同时持有稀有度1和2时，1出现的概率为40，2为60，以此类推
        let rareChances = {
            // 稀有度依次1， 2， 3， 4
            1: ['0-100'], // 只持有稀有度1
            2: ['0-40', '41-100'],
            3: ['0-25', '26-50', '51-100'],
            4: ['0-10', '11-30', '31-60', '61-100'],
        };
        for (let i = 0; i < 3; i++) {
            let objs = threeObjs[i]; // 同种类型不同稀有度
            let chances = rareChances[objs.length];
            objs.sort((x, y) => (x.rare >= y.rare ? 1 : -1));
            let roll = math.randomRangeInt(0, 100);
            for (let y = 0; y < objs.length; y++) {
                let one = objs[y];
                let chanceRange = chances[y].split('-');
                let min = parseInt(chanceRange[0]);
                let max = parseInt(chanceRange[1]);
                if (roll >= min && roll <= max) {
                    cardIDs.push(one.id);
                    cardObjs.push(one);
                    break;
                }
            }
        }

        // 界面
        let container = this.cardsPopupNode.getChildByName('CardsContainer');
        container.removeAllChildren();
        let g = this.cardsPopupNode.getComponent(Graphics) as Graphics;
        g.clear();

        this.cardsPopupNode
            .getComponent(UITransform)
            .setContentSize(new Size(this.winSize.width, this.winSize.height));
        g.fillColor.fromHEX('#111111aa');
        g.fillRect(
            -this.winSize.width / 2,
            -this.winSize.height / 2,
            this.winSize.width,
            this.winSize.height
        );
        g.fill();

        let cardWidth = 205;
        let cardHeight = 270;
        let cardMargin = 20;
        let createCard = (id: number, x: number): Node => {
            let oneCard = instantiate(this.map.oneCardFab);
            oneCard.name = 'card';
            let oneCardS = oneCard.getComponent(OneCard);
            oneCardS.cardID = id;
            oneCardS.cardWidth = cardWidth;
            oneCardS.cardHeight = cardHeight;
            oneCard.setPosition(new Vec3(x, 0, 0));
            oneCard.on(Input.EventType.TOUCH_END, this.onChooseCardEnd, this);
            return oneCard;
        };
        let card1 = createCard(cardIDs[0], -cardWidth - cardMargin);
        let card2 = createCard(cardIDs[1], 0);
        let card3 = createCard(cardIDs[2], cardWidth + cardMargin);

        cardObjs.sort((x, y) => (x.cost >= y.cost ? 1 : -1));
        if (cardObjs[0].cost > this.playerS.currentDirt) {
            this.currentDrawFreeCardID = cardObjs[0].id;
        }
        let createCardBtns = (id: number, cardNode: Node) => {
            let b = instantiate(this.map.startBtnFab);
            let bs = b.getComponent(StartBtn);
            bs.opts.width = cardWidth * 0.8;
            bs.opts.height = 50;
            bs.opts.iconFrame = this.map.dirtFrame;
            bs.btnText = id == this.currentDrawFreeCardID ? '免费' : `${GameConfig.cards[id].cost}`;
            bs.addiData = {
                cardID: id,
                isAd: false,
            };
            bs.delegate = this;
            // bs.onTouchStart = this.onLevelTouch;
            // bs.onTouchCancel = this.onLevelCancel;
            bs.onTouchEnd = this.onChooseCardBtnEnd;
            b.setPosition(
                new Vec3(cardNode.position.x, cardNode.position.y - cardHeight / 2 - 60, 0)
            );
            container.addChild(b);

            let cardConf = GameConfig.cards[id];
            if (this.currentDrawFreeCardID != id && cardConf.cost > this.playerS.currentDirt) {
                this.scheduleOnce(() => {
                    let label = b.getChildByName('Label');
                    // label.getComponent(Label).color = new Color().fromHEX("#000000");
                    label.getComponent(Label).outlineColor = new Color().fromHEX('#eb4040');
                });
            }

            // 创建单个卡片 广告按钮
            if (id !== this.currentDrawFreeCardID && cardConf.cost > this.playerS.currentDirt) {
                let adBtnNode = instantiate(this.map.startBtnFab);
                let adBtnbs = adBtnNode.getComponent(StartBtn);
                adBtnbs.opts.width = cardWidth * 0.8;
                adBtnbs.opts.height = 50;
                adBtnbs.opts.iconFrame = this.map.adFrame;
                adBtnbs.btnText = 'Ad';
                adBtnbs.addiData = {
                    cardID: id,
                    isAd: true,
                };
                adBtnbs.delegate = this;
                adBtnbs.onTouchEnd = (self, event) => {
                    // this.map.adService.showAd((result, rewardType, amount) => {
                    //     if (result === AdResult.EARNED_REWARD) {
                    //         console.log(`选择技能卡：用户完整观看了广告，获得奖励: ${rewardType}, ${amount}`);
                    //         this.onChooseCardBtnEnd(self, event);
                    //     }
                    // });
                    this.onChooseCardBtnEnd(self, event);
                };
                adBtnNode.setPosition(new Vec3(b.position.x, b.position.y - 70, 0));
                container.addChild(adBtnNode);
            }
        };

        // 播放卡片出现动画
        container.addChild(card1);
        card1.setScale(new Vec3(0, 1, 1));
        container.addChild(card2);
        card2.setScale(new Vec3(0, 1, 1));
        container.addChild(card3);
        card3.setScale(new Vec3(0, 1, 1));
        tween(card1)
            .to(
                0.2,
                {
                    scale: new Vec3(1, 1, 1),
                },
                {
                    onComplete: () => {
                        tween(card2)
                            .to(
                                0.2,
                                {
                                    scale: new Vec3(1, 1, 1),
                                },
                                {
                                    onComplete: () => {
                                        tween(card3)
                                            .to(
                                                0.2,
                                                {
                                                    scale: new Vec3(1, 1, 1),
                                                },
                                                {
                                                    onComplete: () => {
                                                        createCardBtns(cardIDs[0], card1);
                                                        createCardBtns(cardIDs[1], card2);
                                                        createCardBtns(cardIDs[2], card3);
                                                    },
                                                }
                                            )
                                            .start();
                                    },
                                }
                            )
                            .start();
                    },
                }
            )
            .start();

        this.drawnCardsRedrawBtn();
    }

    onChooseCardEnd(event: EventTouch) {
        let cardID = event.target.getComponent(OneCard).cardID;
        this.doChooseCard(cardID, false);
    }

    onChooseCardBtnEnd(self: Item, event: EventTouch) {
        let cardID = event.target.getComponent(StartBtn).addiData.cardID;
        let isAd = event.target.getComponent(StartBtn).addiData.isAd;
        self.doChooseCard(cardID, isAd);
    }

    // onChooseCardCancel(self: Item, event: EventTouch) {}
    doChooseCard(cardID: number, isAd: boolean) {
        let container = this.cardsPopupNode.getChildByName('CardsContainer');
        let animDur = 0.3;
        if (cardID === 9999) {
            // 看广告选了 三张卡
            for (let i = 0; i < container.children.length; i++) {
                let one = container.children[i];
                if (one.name !== 'card') continue;
                let id = one.getComponent(OneCard).cardID;
                this.usedCardIDs.push(id);
                let cardConf = GameConfig.cards[id];
                this.handleChooseCard(cardConf);
                tween(one)
                    .by(
                        animDur,
                        {
                            position: new Vec3(0, 100, 0),
                        },
                        {
                            onComplete: () => {},
                        }
                    )
                    .start();
            }
            this.closeCardsPopup();
            EventBus.emit('playSound', 'chooseCard');
            this.playerS.currentDirt = 0;
        } else {
            // 选单个卡
            let cardConf = GameConfig.cards[cardID];
            let cost = this.currentDrawFreeCardID === cardID ? 0 : cardConf.cost;
            if (!isAd) {
                if (this.playerS.currentDirt < cost) return;
                this.playerS.currentDirt -= cost;
            } else {
                // 看广告获得
                this.playerS.currentDirt = 0;
            }
            this.usedCardIDs.push(cardID);
            EventBus.emit('playSound', 'chooseCard');

            this.handleChooseCard(cardConf);

            // 播放选中动画
            for (let i = 0; i < container.children.length; i++) {
                let one = container.children[i];
                if (one.name !== 'card') continue;

                let oneS = one.getComponent(OneCard) as OneCard;
                if (oneS.cardID == cardID) {
                    tween(one)
                        .by(
                            animDur,
                            {
                                position: new Vec3(0, 100, 0),
                            },
                            {
                                onComplete: () => {
                                    this.closeCardsPopup();
                                },
                            }
                        )
                        .start();
                } else {
                    tween(one)
                        .to(animDur, {
                            scale: new Vec3(0, 0, 0),
                        })
                        .start();
                }
            }
        }
    }

    handleChooseCard(cardConf) {
        let buffAreaNode = this.uiNode.getChildByPath('Status/BuffArea');
        // 增加buff图标
        let buff = instantiate(this.map.playerBuffPrefab);
        buff.getComponent(PlayerBuffCtl).icon = cardConf.icon;
        buffAreaNode.addChild(buff);
        if (cardConf.kind == GameConfig.cardsKinds.POWER) {
            this.playerS.power += cardConf.value1;
            this.scheduleOnce(() => {
                // 删除buff图标
                buff.destroy();
                this.playerS.power -= cardConf.value1;
            }, cardConf.dur);
        } else if (cardConf.kind == GameConfig.cardsKinds.HP) {
            // this.playerS.hpInitial += cardConf.value1;
            buff.destroy();
            this.playerS.hp += cardConf.value1;
        } else if (cardConf.kind == GameConfig.cardsKinds.LAMP) {
            // this.playerS.lampInitialPower += cardConf.value1;
            buff.destroy();
            this.playerS.lampPower += cardConf.value1;
        } else if (cardConf.kind == GameConfig.cardsKinds.LAMP_SPEED) {
            this.playerS.lampRunSpeedDelta += -1 * cardConf.value1;
            this.scheduleOnce(() => {
                buff.destroy();
                this.playerS.lampRunSpeedDelta -= -1 * cardConf.value1;
            }, cardConf.dur);
        } else if (cardConf.kind == GameConfig.cardsKinds.MAGNET_RADIUS) {
            this.playerS.magnetRadiusDelta += cardConf.value1 * this.map.tileSize.width;
            this.scheduleOnce(() => {
                buff.destroy();
                this.playerS.magnetRadiusDelta -= cardConf.value1 * this.map.tileSize.width;
            }, cardConf.dur);
        } else if (cardConf.kind == GameConfig.cardsKinds.DIRT_COIN) {
            this.unschedule(this.card_undo_dirtSpawnIcon); // true false类型的必须先撤销，否则使用多个同类型卡片会出问题
            this.playerS.dirtSpawnCoin = true;
            this.scheduleOnce(() => {
                buff.destroy();
                this.card_undo_dirtSpawnIcon();
            }, cardConf.dur);
        } else if (cardConf.kind == GameConfig.cardsKinds.DIG_AREA_HORI) {
            this.playerS.digAreaHoriDelta += cardConf.value1;
            this.scheduleOnce(() => {
                buff.destroy();
                this.playerS.digAreaHoriDelta -= cardConf.value1;
            }, cardConf.dur);
        } else if (cardConf.kind == GameConfig.cardsKinds.DIG_AREA_VERT) {
            this.playerS.digAreaVertDelta += cardConf.value1;
            this.scheduleOnce(() => {
                buff.destroy();
                this.playerS.digAreaVertDelta -= cardConf.value1;
            }, cardConf.dur);
        } else if (cardConf.kind == GameConfig.cardsKinds.THROW_FLYING_SAW) {
            buff.destroy();
            this.card_throwFlyingSaw(cardConf.value1);
        } else if (cardConf.kind == GameConfig.cardsKinds.THROW_ROUND_BLADE) {
            buff.destroy();
            this.card_throwRoundBlade(cardConf.value1);
        } else if (cardConf.kind == GameConfig.cardsKinds.GET_DIRT) {
            buff.destroy();
            this.playerS.currentDirt += cardConf.value1;
            UserData.currentLevelDirtTotalCount += cardConf.value1;
        } else if (cardConf.kind == GameConfig.cardsKinds.GET_DIRT_DELTA) {
            this.playerS.getDirtDelta += cardConf.value1;
            this.scheduleOnce(() => {
                buff.destroy();
                this.playerS.getDirtDelta -= cardConf.value1;
            }, cardConf.dur);
        } else if (cardConf.kind == GameConfig.cardsKinds.DIG_SPEED) {
            this.playerS.autoMoveDelayDelta += cardConf.value1;
            this.scheduleOnce(() => {
                buff.destroy();
                this.playerS.autoMoveDelayDelta -= cardConf.value1;
            }, cardConf.dur);
        } else if (cardConf.kind == GameConfig.cardsKinds.NO_BOMB_DAMAGE) {
            this.playerS.bombDamageDelta -= cardConf.value1;
            this.scheduleOnce(() => {
                buff.destroy();
                this.playerS.bombDamageDelta += cardConf.value1;
            }, cardConf.dur);
        } else if (cardConf.kind == GameConfig.cardsKinds.NO_TRAP_DAMAGE) {
            this.playerS.trapDamageDelta -= cardConf.value1;
            this.scheduleOnce(() => {
                buff.destroy();
                this.playerS.trapDamageDelta += cardConf.value1;
            }, cardConf.dur);
        } else if (cardConf.kind == GameConfig.cardsKinds.HP_FOR_POWER) {
            this.playerS.hp = this.playerS.hp * (1 - cardConf.value1);
            this.playerS.hp = Math.max(1, this.playerS.hp);
            this.playerS.powerDelta += 1;
        } else if (cardConf.kind == GameConfig.cardsKinds.TWIN_BLADE) {
            buff.destroy();
            this.card_throwTwinBlade(cardConf.value1, cardConf.dur);
        } else if (cardConf.kind == GameConfig.cardsKinds.CALL_LADDER) {
            buff.destroy();
            UserData.currentLevelCallLadderCardID = cardConf.id;
            this.useLadder();
        } else if (cardConf.kind == GameConfig.cardsKinds.MINES_DOUBLE) {
            this.playerS.minesPriceRatio = this.playerS.minesPriceRatio * cardConf.value1;
            this.scheduleOnce(() => {
                buff.destroy();
                this.playerS.minesPriceRatio = 1;
            }, cardConf.dur);
        } else if (cardConf.kind == GameConfig.cardsKinds.LAMP_HALF) {
            this.playerS.lampPower = this.playerS.lampPower / 2;
            UserData.currentLevelLampHalfCardID = cardConf.id;
        } else if (cardConf.kind == GameConfig.cardsKinds.ADD_HALF) {
            UserData.currentLevelAddHalfCardID = cardConf.id;
        } else if (cardConf.kind == GameConfig.cardsKinds.RESURGENCE) {
            UserData.currentLevelResurgenceCardID = cardConf.id;
        }
    }

    card_undo_dirtSpawnIcon() {
        this.playerS.dirtSpawnCoin = false;
    }

    card_plantBombs(kind: number, level: number) {
        this.scheduleOnce(() => {
            let minimalX = 2; // 包含
            let maxX = this.map.mapSize.width - 2; // 不包含
            let currentGID = MapUtils.getGIDByLocation(this.node.position);
            for (let i = minimalX; i < maxX; i++) {
                let bomb = this.plantBomb(kind);
                let pos = MapUtils.getLocationByGID(new Vec3(i, currentGID.y, 0));
                pos.y = bomb.position.y;
                bomb.setPosition(pos);
                bomb.getComponent(Bomb).level = level;
            }
        }, 0.6);
    }

    card_throwFlyingSaw(level: number) {
        this.scheduleOnce(() => {
            this.map.spawnSawAtPos(this.playerS.node.position, level);
        }, 0.6);
    }

    card_throwRoundBlade(dur: number) {
        this.scheduleOnce(() => {
            this.map.spawnRoundBladeAtPos(this.playerS.node.position, dur);
        }, 0.6);
    }

    card_throwTwinBlade(damage: number, dur: number) {
        this.scheduleOnce(() => {
            this.map.spawnTwinBladeAtPos(this.playerS.node.position, damage, dur);
        }, 0.6);
    }

    closeCardsPopup() {
        // 恢复显示粘土原始层级
        director.getScene().getChildByPath('Canvas/UI/Dirt').getComponent(UITransform).priority =
            GameConfig.UIPriority.lampMask - 1;

        this.currentDrawFreeCardID = null;
        this.cardsPopupNode.active = false;
        GameConfig.currentGameState = GameConfig.gameStates.RUNNING;
        // 使用了梯子技能卡
        if (UserData.currentLevelCallLadderCardID !== -1) {
            GameConfig.currentGameState = GameConfig.gameStates.GAMEOVER;
        }

        let cardsContainer = this.cardsPopupNode.getChildByName('CardsContainer');
        cardsContainer.removeAllChildren();
        let btnsContainer = this.cardsPopupNode.getChildByName('BtnsContainer');
        btnsContainer.removeAllChildren();
    }
}
