import { _decorator, Component, SpriteFrame } from 'cc';
import { GameConfig } from '../../GameConfig';
import { Prefab } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('CommonAssetsCtl')
export class CommonAssetsCtl extends Component {

    // 图片资源
    // 开始游戏前页面背景
    @property(SpriteFrame)
    public chapterBg1TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg1BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg1IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg2TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg2BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg2IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg3TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg3BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg3IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg4TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg4BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg4IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg5TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg5BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg5IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg6TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg6BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg6IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg7TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg7BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg7IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg8TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg8BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg8IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg9TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg9BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg9IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg10TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg10BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg10IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg11TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg11BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg11IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg12TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg12BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg12IconFrame: SpriteFrame;

    @property(SpriteFrame)
    public chapterBg13TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg13BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg13IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg14TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg14BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg14IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg15TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg15BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg15IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg16TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg16BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg16IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg17TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg17BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg17IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg18TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg18BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg18IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg19TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg19BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg19IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg20TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg20BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg20IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg21TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg21BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg21IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg22TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg22BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg22IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg23TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg23BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg23IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg24TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg24BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg24IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg25TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg25BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg25IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg26TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg26BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg26IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg27TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg27BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg27IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg28TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg28BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg28IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg29TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg29BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg29IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg30TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg30BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg30IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg31TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg31BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg31IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg32TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg32BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg32IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg33TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg33BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg33IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg34TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg34BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg34IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg35TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg35BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg35IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg36TopFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg36BottomFrame: SpriteFrame;
    @property(SpriteFrame)
    public chapterBg36IconFrame: SpriteFrame;



    // 地刺
    @property(SpriteFrame)
    public trapDesetSpikeFrame: SpriteFrame;
    @property(SpriteFrame)
    public trapIceSpikeFrame: SpriteFrame;
    @property(SpriteFrame)
    public trapVolcanoSpikeFrame: SpriteFrame;

    // 石头
    @property(SpriteFrame)
    public trapDwarfRockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trapIceRockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trapVolcanoRockFrame: SpriteFrame;

    @property(SpriteFrame)
    public trapAncientRockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trapBadlandsRockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trapCastleRockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trapDesetRockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trapDungeonRockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trapInfernoRockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trapJungleRockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trapMedievalRockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trapSpookyRockFrame: SpriteFrame;

    @property(SpriteFrame)
    public trap13RockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trap14RockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trap15RockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trap16RockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trap17RockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trap18RockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trap19RockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trap20RockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trap21RockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trap22RockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trap23RockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trap24RockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trap25RockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trap26RockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trap27RockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trap28RockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trap29RockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trap30RockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trap31RockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trap32RockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trap33RockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trap34RockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trap35RockFrame: SpriteFrame;
    @property(SpriteFrame)
    public trap36RockFrame: SpriteFrame;


    // 问号
    @property(SpriteFrame)
    itemDwarfBucketFrame: SpriteFrame;
    @property(SpriteFrame)
    itemIceBucketFrame: SpriteFrame;
    @property(SpriteFrame)
    itemVolcanoBucketFrame: SpriteFrame;


    @property(SpriteFrame)
    itemAncientBucketFrame: SpriteFrame;
    @property(SpriteFrame)
    itemBadlandsBucketFrame: SpriteFrame;
    @property(SpriteFrame)
    itemCastleBucketFrame: SpriteFrame;
    @property(SpriteFrame)
    itemDesertBucketFrame: SpriteFrame;
    @property(SpriteFrame)
    itemDungeonBucketFrame: SpriteFrame;
    @property(SpriteFrame)
    itemInfernoBucketFrame: SpriteFrame;
    @property(SpriteFrame)
    itemJungleBucketFrame: SpriteFrame;
    @property(SpriteFrame)
    itemMedievalBucketFrame: SpriteFrame;
    @property(SpriteFrame)
    itemSpookyBucketFrame: SpriteFrame;

    @property(SpriteFrame)
    item13BucketFrame: SpriteFrame;
    @property(SpriteFrame)
    item14BucketFrame: SpriteFrame;
    @property(SpriteFrame)
    item15BucketFrame: SpriteFrame;
    @property(SpriteFrame)
    item16BucketFrame: SpriteFrame;
    @property(SpriteFrame)
    item17BucketFrame: SpriteFrame;
    @property(SpriteFrame)
    item18BucketFrame: SpriteFrame;
    @property(SpriteFrame)
    item19BucketFrame: SpriteFrame;
    @property(SpriteFrame)
    item20BucketFrame: SpriteFrame;
    @property(SpriteFrame)
    item21BucketFrame: SpriteFrame;
    @property(SpriteFrame)
    item22BucketFrame: SpriteFrame;
    @property(SpriteFrame)
    item23BucketFrame: SpriteFrame;
    @property(SpriteFrame)
    item24BucketFrame: SpriteFrame;
    @property(SpriteFrame)
    item25BucketFrame: SpriteFrame;
    @property(SpriteFrame)
    item26BucketFrame: SpriteFrame;
    @property(SpriteFrame)
    item27BucketFrame: SpriteFrame;
    @property(SpriteFrame)
    item28BucketFrame: SpriteFrame;
    @property(SpriteFrame)
    item29BucketFrame: SpriteFrame;
    @property(SpriteFrame)
    item30BucketFrame: SpriteFrame;
    @property(SpriteFrame)
    item31BucketFrame: SpriteFrame;
    @property(SpriteFrame)
    item32BucketFrame: SpriteFrame;
    @property(SpriteFrame)
    item33BucketFrame: SpriteFrame;
    @property(SpriteFrame)
    item34BucketFrame: SpriteFrame;
    @property(SpriteFrame)
    item35BucketFrame: SpriteFrame;
    @property(SpriteFrame)
    item36BucketFrame: SpriteFrame;

    @property(SpriteFrame)
    itemHeartFrame: SpriteFrame;
    @property(SpriteFrame)
    itemLampFrame: SpriteFrame;
    @property(SpriteFrame)
    itemCoinFrame: SpriteFrame;
    @property(SpriteFrame)
    itemKeyFrame: SpriteFrame;
    @property(SpriteFrame)
    itemDiamondFrame: SpriteFrame;
    @property(SpriteFrame)
    hiddenRoomEntranceFrame: SpriteFrame; // 隐藏房间入口火山图片


    // 装备卡
    @property(SpriteFrame)
    public cardBgRare1Frame: SpriteFrame;
    @property(SpriteFrame)
    public cardBgRare2Frame: SpriteFrame;
    @property(SpriteFrame)
    public cardBgRare3Frame: SpriteFrame;
    @property(SpriteFrame)
    public cardBgRare4Frame: SpriteFrame;
    @property(SpriteFrame)
    public cardIconBgRare1Frame: SpriteFrame;
    @property(SpriteFrame)
    public cardIconBgRare2Frame: SpriteFrame;
    @property(SpriteFrame)
    public cardIconBgRare3Frame: SpriteFrame;
    @property(SpriteFrame)
    public cardIconBgRare4Frame: SpriteFrame;

    @property(SpriteFrame)
    public cardIconPowerFrame: SpriteFrame;
    @property(SpriteFrame)
    public cardIconHPFrame: SpriteFrame;
    @property(SpriteFrame)
    public cardIconLampFrame: SpriteFrame;
    @property(SpriteFrame)
    public cardIconLampSpeedFrame: SpriteFrame;
    @property(SpriteFrame)
    public cardIconMagnetRadiusFrame: SpriteFrame;
    @property(SpriteFrame)
    public cardIconDirtCoinFrame: SpriteFrame;
    @property(SpriteFrame)
    public cardIconDigAreaHoriFrame: SpriteFrame;
    @property(SpriteFrame)
    public cardIconDigAreaVertFrame: SpriteFrame;
    @property(SpriteFrame)
    public cardIconPlantFlyingBombsFrame: SpriteFrame;
    @property(SpriteFrame)
    public cardIconPlantGrenadeBombsFrame: SpriteFrame;
    @property(SpriteFrame)
    public cardIconGetDirtFrame: SpriteFrame;
    @property(SpriteFrame)
    public cardIconGetDirtDeltaFrame: SpriteFrame;
    @property(SpriteFrame)
    public cardIconDigSpeedFrame: SpriteFrame;
    @property(SpriteFrame)
    public cardIconNoBombDamageFrame: SpriteFrame;
    @property(SpriteFrame)
    public cardIconNoTrapDamageFrame: SpriteFrame;
    @property(SpriteFrame)
    public cardIconHPForPowerFrame: SpriteFrame;
    @property(SpriteFrame)
    public cardIconExplodeDigFrame: SpriteFrame;
    @property(SpriteFrame)
    public cardIconCallLadderFrame: SpriteFrame;
    @property(SpriteFrame)
    public cardIconMinesDoubleFrame: SpriteFrame;
    @property(SpriteFrame)
    public cardIconLampHalfFrame: SpriteFrame;
    @property(SpriteFrame)
    public cardIconAddHalfFrame: SpriteFrame;
    @property(SpriteFrame)
    public cardIconResurgenceFrame: SpriteFrame;

    @property(SpriteFrame)
    public cardStarFilledFrame: SpriteFrame;
    @property(SpriteFrame)
    public cardStarEmptyFrame: SpriteFrame;
    // 装备卡槽
    @property(SpriteFrame)
    public slotIconBgRare1Frame: SpriteFrame; // 绿
    @property(SpriteFrame)
    public slotIconBgRare2Frame: SpriteFrame; // 蓝
    @property(SpriteFrame)
    public slotIconBgRare3Frame: SpriteFrame; // 紫
    @property(SpriteFrame)
    public slotIconBgRare4Frame: SpriteFrame; // 橙
    @property(SpriteFrame)
    public slotIconBgDefaultFrame: SpriteFrame; //默认
    @property(SpriteFrame)
    public slotIconEmptyFrame: SpriteFrame; // 加号
    @property(SpriteFrame)
    public slotIconLockedFrame: SpriteFrame; // 锁

    // 武器
    @property(SpriteFrame)
    public weaponBg: SpriteFrame;
    @property(SpriteFrame)
    public weapon1Frame: SpriteFrame;
    @property(SpriteFrame)
    public weapon2Frame: SpriteFrame;
    @property(SpriteFrame)
    public weapon3Frame: SpriteFrame;

    // 关卡目标
    @property(SpriteFrame)
    public levelTargetmineBgFrame: SpriteFrame;
    // 矿石根据类型不同，需要不同的图标
    // @property(SpriteFrame)
    // public levelTargetMineIconFrame: SpriteFrame;
    @property(SpriteFrame)
    public levelTargetdepthBgFrame: SpriteFrame;
    @property(SpriteFrame)
    public levelTargetdepthIconFrame: SpriteFrame;
    @property(SpriteFrame)
    public levelTargetcoinBgFrame: SpriteFrame;
    @property(SpriteFrame)
    public levelTargetcoinIconFrame: SpriteFrame;
    // 关卡奖励
    @property(SpriteFrame)
    public levelRewardcoinBgFrame: SpriteFrame;
    @property(SpriteFrame)
    public levelRewardcoinIconFrame: SpriteFrame;
    @property(SpriteFrame)
    public levelRewardMultiCoinsIconFrame: SpriteFrame;
    @property(SpriteFrame)
    public levelRewarddiamondBgFrame: SpriteFrame;
    @property(SpriteFrame)
    public levelRewarddiamondIconFrame: SpriteFrame;
    @property(SpriteFrame)
    public levelRewardkeyBgFrame: SpriteFrame;
    @property(SpriteFrame)
    public levelRewardkeyIconFrame: SpriteFrame;
    @property(SpriteFrame)
    public levelRewardcardBgFrame: SpriteFrame;
    @property(SpriteFrame)
    public levelRewardcardIconFrame: SpriteFrame;
    @property(SpriteFrame)
    public levelRewardcardPartBgFrame: SpriteFrame;
    @property(SpriteFrame)
    public levelRewardcardPart1IconFrame: SpriteFrame;
    @property(SpriteFrame)
    public levelRewardcardPartI2conFrame: SpriteFrame;
    @property(SpriteFrame)
    public levelRewardcardPartI3conFrame: SpriteFrame;
    @property(SpriteFrame)
    public levelRewardcardPartI4conFrame: SpriteFrame;
    @property(SpriteFrame)
    public levelRewardexpBgFrame: SpriteFrame;
    @property(SpriteFrame)
    public levelRewardexpIconFrame: SpriteFrame;

    // 矿石
    @property(SpriteFrame)
    m19ExpFrame: SpriteFrame; // 经验值矿石
    // desert
    @property(SpriteFrame)
    m20DesertBasicFrame: SpriteFrame; // 基础矿石，最不值钱的，大量存在
    @property(SpriteFrame)
    m26Frame: SpriteFrame;
    @property(SpriteFrame)
    m31Frame: SpriteFrame;
    @property(SpriteFrame)
    m30Frame: SpriteFrame;
    @property(SpriteFrame)
    m25Frame: SpriteFrame;
    @property(SpriteFrame)
    m42Frame: SpriteFrame;
    @property(SpriteFrame)
    m45Frame: SpriteFrame;
    // ice
    @property(SpriteFrame)
    m36IceBasicFrame: SpriteFrame; // 基础矿石，最不值钱的，大量存在
    @property(SpriteFrame)
    m28Frame: SpriteFrame;
    @property(SpriteFrame)
    m34Frame: SpriteFrame;
    @property(SpriteFrame)
    m48Frame: SpriteFrame;
    @property(SpriteFrame)
    m50Frame: SpriteFrame;
    @property(SpriteFrame)
    m46Frame: SpriteFrame;
    @property(SpriteFrame)
    m44Frame: SpriteFrame;
    // volcano
    @property(SpriteFrame)
    m24VolcanoBasicFrame: SpriteFrame; // 基础矿石，最不值钱的，大量存在
    @property(SpriteFrame)
    m27Frame: SpriteFrame;
    @property(SpriteFrame)
    m35Frame: SpriteFrame;
    @property(SpriteFrame)
    m37Frame: SpriteFrame;
    @property(SpriteFrame)
    m38Frame: SpriteFrame;
    @property(SpriteFrame)
    m39Frame: SpriteFrame;
    @property(SpriteFrame)
    m41Frame: SpriteFrame;

    // 商店
    @property(SpriteFrame)
    storeAdIconFrame: SpriteFrame;
    @property(SpriteFrame)
    storeBadgeBESTFrame: SpriteFrame;
    @property(SpriteFrame)
    storeBadgeHOTFrame: SpriteFrame;
    @property(SpriteFrame)
    storeDiamond1Frame: SpriteFrame;
    @property(SpriteFrame)
    storeDiamond2Frame: SpriteFrame;
    @property(SpriteFrame)
    storeDiamond3Frame: SpriteFrame;
    @property(SpriteFrame)
    storeDiamond4Frame: SpriteFrame;
    @property(SpriteFrame)
    storeAdFrame: SpriteFrame;

    // Disable按钮
    @property(SpriteFrame)
    disableBtnFrame: SpriteFrame;
    @property(SpriteFrame)
    greenBtnFrame: SpriteFrame;

    // upgrade
    @property(SpriteFrame)
    upgradeVerticalStickBlockFrame: SpriteFrame;
    @property(SpriteFrame)
    upgradeVerticalStickFrame: SpriteFrame;
    @property(SpriteFrame)
    upgradeHorizontalStickFrame: SpriteFrame;
    @property(SpriteFrame)
    upgradeSlotBlockFrame: SpriteFrame;
    @property(SpriteFrame)
    upgradeSlotFrame: SpriteFrame;
    @property(SpriteFrame)
    upgradeMedalBlockFrame: SpriteFrame;
    @property(SpriteFrame)
    upgradeMedalFrame: SpriteFrame;
    @property(SpriteFrame)
    upgradeHPBlockFrame: SpriteFrame;
    @property(SpriteFrame)
    upgradeHPFrame: SpriteFrame;
    @property(SpriteFrame)
    upgradePowerBlockFrame: SpriteFrame;
    @property(SpriteFrame)
    upgradePowerFrame: SpriteFrame;

    // 宝箱
    @property(SpriteFrame)
    juniorBoxFrame: SpriteFrame;
    @property(SpriteFrame)
    juniorOpeningBoxFrame: SpriteFrame;
    @property(SpriteFrame)
    juniorOpenedBoxFrame: SpriteFrame;
    @property(SpriteFrame)
    intermediateBoxFrame: SpriteFrame;
    @property(SpriteFrame)
    intermediateOpeningBoxFrame: SpriteFrame;
    @property(SpriteFrame)
    intermediateOpenedBoxFrame: SpriteFrame;
    @property(SpriteFrame)
    seniorBoxFrame: SpriteFrame;
    @property(SpriteFrame)
    seniorOpeningBoxFrame: SpriteFrame;
    @property(SpriteFrame)
    seniorOpenedBoxFrame: SpriteFrame;

    // 宠物
    @property(SpriteFrame)
    pet1Frame: SpriteFrame;
    @property(Prefab)
    pet1Fab: Prefab;
    @property(SpriteFrame)
    pet2Frame: SpriteFrame;
    @property(Prefab)
    pet2Fab: Prefab;
    @property(SpriteFrame)
    pet3Frame: SpriteFrame;
    @property(Prefab)
    pet3Fab: Prefab;
    @property(SpriteFrame)
    pet4Frame: SpriteFrame;
    @property(Prefab)
    pet4Fab: Prefab;
    // 宠物Border
    @property(SpriteFrame)
    petLockFrame: SpriteFrame;
    @property(SpriteFrame)
    petCheckedFrame: SpriteFrame;
    @property(SpriteFrame)
    petUncheckedFrame: SpriteFrame;
    // 宠物Diabled按钮
    @property(SpriteFrame)
    petDisableBtnFrame: SpriteFrame;
    // 宠物碎片
    @property(SpriteFrame)
    pet1ChipFrame: SpriteFrame;
    @property(SpriteFrame)
    pet2ChipFrame: SpriteFrame;
    @property(SpriteFrame)
    pet3ChipFrame: SpriteFrame;
    @property(SpriteFrame)
    pet4ChipFrame: SpriteFrame;
    // 武器Border
    @property(SpriteFrame)
    weaponUncheckedBorderFrame: SpriteFrame;
    @property(SpriteFrame)
    weaponCheckedBorderFrame: SpriteFrame;

    // 关卡进度条
    @property(SpriteFrame)
    levelProgressCircleFrame: SpriteFrame;
    @property(SpriteFrame)
    levelProgressCircleDoneFrame: SpriteFrame;
    @property(SpriteFrame)
    levelProgressRectDoneFrame: SpriteFrame;

    // 地图块 chapter 1
    @property(SpriteFrame)
    chapter1MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter1MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter1MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter1MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter1MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter1MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter1MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter1MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter1MapBottomCenterFrame: SpriteFrame; // 最下中间的
    // 地图块 chapter 2
    @property(SpriteFrame)
    chapter2MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter2MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter2MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter2MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter2MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter2MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter2MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter2MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter2MapBottomCenterFrame: SpriteFrame; // 最下中间的
    // 地图块 chapter 3
    @property(SpriteFrame)
    chapter3MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter3MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter3MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter3MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter3MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter3MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter3MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter3MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter3MapBottomCenterFrame: SpriteFrame; // 最下中间的
    // 地图块 chapter 4
    @property(SpriteFrame)
    chapter4MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter4MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter4MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter4MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter4MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter4MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter4MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter4MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter4MapBottomCenterFrame: SpriteFrame; // 最下中间的

    // 地图块 chapter 5
    @property(SpriteFrame)
    chapter5MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter5MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter5MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter5MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter5MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter5MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter5MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter5MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter5MapBottomCenterFrame: SpriteFrame; // 最下中间的


    // 地图块 chapter 6
    @property(SpriteFrame)
    chapter6MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter6MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter6MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter6MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter6MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter6MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter6MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter6MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter6MapBottomCenterFrame: SpriteFrame; // 最下中间的

    // 地图块 chapter 7
    @property(SpriteFrame)
    chapter7MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter7MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter7MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter7MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter7MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter7MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter7MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter7MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter7MapBottomCenterFrame: SpriteFrame; // 最下中间的

    // 地图块 chapter 8
    @property(SpriteFrame)
    chapter8MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter8MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter8MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter8MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter8MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter8MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter8MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter8MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter8MapBottomCenterFrame: SpriteFrame; // 最下中间的

    // 地图块 chapter 9
    @property(SpriteFrame)
    chapter9MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter9MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter9MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter9MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter9MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter9MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter9MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter9MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter9MapBottomCenterFrame: SpriteFrame; // 最下中间的

    // 地图块 chapter 10
    @property(SpriteFrame)
    chapter10MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter10MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter10MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter10MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter10MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter10MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter10MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter10MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter10MapBottomCenterFrame: SpriteFrame; // 最下中间的

    // 地图块 chapter 11
    @property(SpriteFrame)
    chapter11MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter11MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter11MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter11MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter11MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter11MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter11MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter11MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter11MapBottomCenterFrame: SpriteFrame; // 最下中间的

    // 地图块 chapter 12
    @property(SpriteFrame)
    chapter12MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter12MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter12MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter12MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter12MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter12MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter12MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter12MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter12MapBottomCenterFrame: SpriteFrame; // 最下中间的

    // 地图块 chapter 13
    @property(SpriteFrame)
    chapter13MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter13MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter13MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter13MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter13MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter13MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter13MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter13MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter13MapBottomCenterFrame: SpriteFrame; // 最下中间的

    // 地图块 chapter 14
    @property(SpriteFrame)
    chapter14MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter14MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter14MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter14MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter14MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter14MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter14MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter14MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter14MapBottomCenterFrame: SpriteFrame; // 最下中间的

    // 地图块 chapter 15
    @property(SpriteFrame)
    chapter15MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter15MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter15MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter15MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter15MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter15MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter15MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter15MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter15MapBottomCenterFrame: SpriteFrame; // 最下中间的

    // 地图块 chapter 16
    @property(SpriteFrame)
    chapter16MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter16MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter16MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter16MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter16MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter16MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter16MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter16MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter16MapBottomCenterFrame: SpriteFrame; // 最下中间的

    // 地图块 chapter 17
    @property(SpriteFrame)
    chapter17MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter17MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter17MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter17MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter17MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter17MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter17MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter17MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter17MapBottomCenterFrame: SpriteFrame; // 最下中间的

    // 地图块 chapter 18
    @property(SpriteFrame)
    chapter18MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter18MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter18MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter18MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter18MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter18MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter18MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter18MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter18MapBottomCenterFrame: SpriteFrame; // 最下中间的

    // 地图块 chapter 19
    @property(SpriteFrame)
    chapter19MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter19MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter19MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter19MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter19MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter19MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter19MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter19MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter19MapBottomCenterFrame: SpriteFrame; // 最下中间的

    // 地图块 chapter 20
    @property(SpriteFrame)
    chapter20MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter20MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter20MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter20MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter20MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter20MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter20MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter20MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter20MapBottomCenterFrame: SpriteFrame; // 最下中间的

    // 地图块 chapter 21
    @property(SpriteFrame)
    chapter21MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter21MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter21MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter21MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter21MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter21MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter21MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter21MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter21MapBottomCenterFrame: SpriteFrame; // 最下中间的

    // 地图块 chapter 22
    @property(SpriteFrame)
    chapter22MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter22MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter22MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter22MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter22MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter22MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter22MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter22MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter22MapBottomCenterFrame: SpriteFrame; // 最下中间的

    // 地图块 chapter 23
    @property(SpriteFrame)
    chapter23MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter23MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter23MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter23MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter23MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter23MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter23MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter23MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter23MapBottomCenterFrame: SpriteFrame; // 最下中间的

    // 地图块 chapter 24
    @property(SpriteFrame)
    chapter24MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter24MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter24MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter24MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter24MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter24MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter24MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter24MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter24MapBottomCenterFrame: SpriteFrame; // 最下中间的

    // 地图块 chapter 25
    @property(SpriteFrame)
    chapter25MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter25MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter25MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter25MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter25MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter25MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter25MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter25MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter25MapBottomCenterFrame: SpriteFrame; // 最下中间的

    // 地图块 chapter 26
    @property(SpriteFrame)
    chapter26MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter26MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter26MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter26MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter26MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter26MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter26MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter26MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter26MapBottomCenterFrame: SpriteFrame; // 最下中间的

    // 地图块 chapter 27
    @property(SpriteFrame)
    chapter27MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter27MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter27MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter27MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter27MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter27MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter27MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter27MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter27MapBottomCenterFrame: SpriteFrame; // 最下中间的

    // 地图块 chapter 28
    @property(SpriteFrame)
    chapter28MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter28MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter28MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter28MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter28MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter28MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter28MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter28MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter28MapBottomCenterFrame: SpriteFrame; // 最下中间的

    // 地图块 chapter 29
    @property(SpriteFrame)
    chapter29MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter29MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter29MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter29MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter29MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter29MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter29MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter29MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter29MapBottomCenterFrame: SpriteFrame; // 最下中间的

    // 地图块 chapter 30
    @property(SpriteFrame)
    chapter30MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter30MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter30MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter30MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter30MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter30MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter30MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter30MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter30MapBottomCenterFrame: SpriteFrame; // 最下中间的

    // 地图块 chapter 31
    @property(SpriteFrame)
    chapter31MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter31MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter31MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter31MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter31MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter31MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter31MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter31MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter31MapBottomCenterFrame: SpriteFrame; // 最下中间的

    // 地图块 chapter 32
    @property(SpriteFrame)
    chapter32MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter32MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter32MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter32MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter32MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter32MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter32MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter32MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter32MapBottomCenterFrame: SpriteFrame; // 最下中间的

    // 地图块 chapter 33
    @property(SpriteFrame)
    chapter33MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter33MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter33MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter33MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter33MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter33MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter33MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter33MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter33MapBottomCenterFrame: SpriteFrame; // 最下中间的

    // 地图块 chapter 34
    @property(SpriteFrame)
    chapter34MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter34MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter34MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter34MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter34MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter34MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter34MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter34MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter34MapBottomCenterFrame: SpriteFrame; // 最下中间的

    // 地图块 chapter 35
    @property(SpriteFrame)
    chapter35MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter35MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter35MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter35MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter35MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter35MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter35MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter35MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter35MapBottomCenterFrame: SpriteFrame; // 最下中间的

    // 地图块 chapter 36
    @property(SpriteFrame)
    chapter36MapTopLeftFrame: SpriteFrame; // 最上左
    @property(SpriteFrame)
    chapter36MapTopRightFrame: SpriteFrame; // 最上右
    @property(SpriteFrame)
    chapter36MapTopCenterFrame: SpriteFrame; // 最上中间的
    @property(SpriteFrame)
    chapter36MapMiddleLeftFrame: SpriteFrame; // 中间左
    @property(SpriteFrame)
    chapter36MapMiddleRightFrame: SpriteFrame; // 中间右
    @property(SpriteFrame)
    chapter36MapMiddleCenterFrame: SpriteFrame; // 中间中间的
    @property(SpriteFrame)
    chapter36MapBottomLeftFrame: SpriteFrame; // 最下左
    @property(SpriteFrame)
    chapter36MapBottomRightFrame: SpriteFrame; // 最下右
    @property(SpriteFrame)
    chapter36MapBottomCenterFrame: SpriteFrame; // 最下中间的

    start() {

    }

    public GetMineFrame(id: number) {
        let f = null;
        if (id == GameConfig.blockKinds.MINE_A19) {
            f = this.m19ExpFrame;
        } else if (id == GameConfig.blockKinds.MINE_A20) {
            f = this.m20DesertBasicFrame;
        } else if (id == GameConfig.blockKinds.MINE_A26) {
            f = this.m26Frame;
        } else if (id == GameConfig.blockKinds.MINE_A31) {
            f = this.m31Frame;
        } else if (id == GameConfig.blockKinds.MINE_A30) {
            f = this.m30Frame;
        } else if (id == GameConfig.blockKinds.MINE_A25) {
            f = this.m25Frame;
        } else if (id == GameConfig.blockKinds.MINE_A42) {
            f = this.m42Frame;
        } else if (id == GameConfig.blockKinds.MINE_A45) {
            f = this.m45Frame;
        } else if (id == GameConfig.blockKinds.MINE_A36) {
            f = this.m36IceBasicFrame;
        } else if (id == GameConfig.blockKinds.MINE_A28) {
            f = this.m28Frame;
        } else if (id == GameConfig.blockKinds.MINE_A34) {
            f = this.m34Frame;
        } else if (id == GameConfig.blockKinds.MINE_A48) {
            f = this.m48Frame;
        } else if (id == GameConfig.blockKinds.MINE_A50) {
            f = this.m50Frame;
        } else if (id == GameConfig.blockKinds.MINE_A46) {
            f = this.m46Frame;
        } else if (id == GameConfig.blockKinds.MINE_A44) {
            f = this.m44Frame;
        } else if (id == GameConfig.blockKinds.MINE_A24) {
            f = this.m24VolcanoBasicFrame;
        } else if (id == GameConfig.blockKinds.MINE_A27) {
            f = this.m27Frame;
        } else if (id == GameConfig.blockKinds.MINE_A35) {
            f = this.m35Frame;
        } else if (id == GameConfig.blockKinds.MINE_A37) {
            f = this.m37Frame;
        } else if (id == GameConfig.blockKinds.MINE_A38) {
            f = this.m38Frame;
        } else if (id == GameConfig.blockKinds.MINE_A39) {
            f = this.m39Frame;
        } else if (id == GameConfig.blockKinds.MINE_A41) {
            f = this.m41Frame;
        }
        return f;
    }
}
