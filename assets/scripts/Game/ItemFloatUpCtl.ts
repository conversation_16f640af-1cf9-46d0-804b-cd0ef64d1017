import { director } from 'cc';
import { Sprite } from 'cc';
import { _decorator, Component } from 'cc';
import { CommonAssetsCtl } from '../v1/Common/CommonAssetsCtl';
import { Label } from 'cc';
import { tween } from 'cc';
import { Vec3 } from 'cc';
import { UITransform } from 'cc';
const { ccclass } = _decorator;

@ccclass('ItemFloatUpCtl')
export class ItemFloatUpCtl extends Component {
    public commonAssets;

    // 向上漂浮效果

    public kind: string; // Coin金币、Heart心、Lamp灯时长
    public count: number;

    start() {
        this.commonAssets = director.getScene().getChildByPath("Canvas/CommonAssets").getComponent(CommonAssetsCtl);

        this.node.getChildByName("Sprite").getComponent(Sprite).spriteFrame = this.commonAssets[`item${this.kind}Frame`];
        this.node.getChildByName("Label").getComponent(Label).string = `+${this.count.toString()}`;

        if (this.kind === "Heart") {
            this.node.getChildByName("Sprite").getComponent(UITransform).setContentSize(30, 30);
        } else if (this.kind === "Lamp") {
            this.node.getChildByName("Sprite").getComponent(UITransform).setContentSize(50, 50);
        }

        tween(this.node).by(0.6, {
            position: new Vec3(0, 100, 0)
        }, {
            onComplete: () => {
                this.node.destroy();
            }
        }).start();
    }

}
