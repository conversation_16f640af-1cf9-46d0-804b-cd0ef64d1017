import {
    _decorator,
    Component,
    instantiate,
    Label,
    Prefab,
    tween,
    Vec3,
} from "cc";
import { EventBus } from "../EventBus";
import { UserData } from "../Utils/UserData";
const { ccclass, property } = _decorator;

@ccclass("coinCtl")
export class coinCtl extends Component {
    @property(Prefab)
    coinItem: Prefab = null!;

    @property
    endCount = 0;

    start() {
        EventBus.on("subCoin", this.settle, this);
        EventBus.on("addCoin", this.addCoin, this);
        EventBus.on("coinToEnd", this.coinToEnd, this);
    }

    coinToEnd() {
        const coin = this.node.getChildByName("Label").getComponent(Label).string;
        this.endCount = Number(coin);
    }

    addCoin() {
        const coin = this.node.getChildByName("Label").getComponent(Label).string;
        const toinNum = Math.floor(Number(coin) * 2);
        const nod = instantiate(this.coinItem);
        nod.getChildByName("Label").getComponent(Label).string = "+" + toinNum;
        nod.setPosition(this.node.getPosition().x, this.node.getPosition().y + 20, 0);
        this.node.addChild(nod);
        this.endCount = toinNum;
        tween(nod)
            .to(
                0.5,
                {
                    position: new Vec3(0, 0, 0),
                },
                {
                    easing: "linear",
                    onComplete: () => {
                        this.node.getChildByName("Label").getComponent(Label).string =
                            toinNum.toString();
                        nod.destroy();
                    },
                }
            )
            .start();
    }

    settle() {
        const coin = this.node.getChildByName("Label").getComponent(Label).string;
        const toinNum = Math.floor(Number(coin) / 2);

        const nod = instantiate(this.coinItem);
        nod.getChildByName("Label").getComponent(Label).string = "-" + toinNum;
        nod.setPosition(this.node.getPosition());
        this.node.addChild(nod);
        this.endCount = toinNum;
        tween(nod)
            .to(
                0.5,
                {
                    position: new Vec3(0, 100, 0),
                },
                {
                    easing: "linear",
                    onComplete: () => {
                        this.node.getChildByName("Label").getComponent(Label).string =
                            toinNum.toString();
                        nod.destroy();
                    },
                }
            )
            .start();
    }

    update() {
        if (this.endCount === UserData.money) return;

        this.node.getChildByName('Label').getComponent(Label).string = UserData.money.toString();
    }
}
