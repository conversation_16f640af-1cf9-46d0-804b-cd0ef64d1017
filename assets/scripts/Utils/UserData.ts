export const UserData: {
    removeAds: boolean;
    isPlaySound: boolean;
    isPlayBgMusic: boolean;
    warehouse: Array<{ name: string; count: number }>;
    flyBoom: number;
    downBoom: number;
    boom: number;
    ladder: number;
    backpack: number;
    isBuyFlyBoom: boolean;
    isBuyDownBoom: boolean;
    isBuyBoom: boolean;
    isBuyLadder: boolean;
    level: number;
    money: number;
    currentLevelBoomDps: number;
    boomScope: number;

    userSkill: string[];
    light: number;
    heart: number;
    power: number;
    iceLock: boolean;
    fireLock: boolean;
    userLocks: string[];
    useTicketAt: string;

    currentPetID: number;
    petsStorage: { [key: number]: number };
    petsFragments: { [key: number]: number };
    gearCards: number[];
    equippedCards: number[];
    canEquipCardsNum: number;
    currentWeapon: number;
    weaponsStorage: number[];
    diamonds: number;
    keys: number;
    grade: number;
    gradeRatio: number;
    currentExp: number;
    currentChapter: number,
    lastChapter: number,
    currentLevel: number,
    lastLevel: number,
    currentLevelKeyCount: number,
    currentLevelDiamondCount: number,
    currentLevelDirtTotalCount: number,
    fragments: { [key: number]: number }
    selectedCard: number;
    cards: number[];
    currentLevelupUnlocksIndex: number;
    currentLevelupUnlocksSubIndex: number;
    nextScene: string | null;
    nextSceneAction: string | null;
    currentLevelCallLadderCardID: number;
    currentLevelLampHalfCardID: number;
    currentLevelAddHalfCardID: number;
    currentLevelResurgenceCardID: number;
    currentLevelAdDoubleCoinRatio: number;
    joystickPos: string;
    joystickSize: string;
    isFirstOpen: boolean;
    hasFoundMines: number[];
    footerTabsIsLock: { [key: string]: boolean };
    commentCount: number;
    nextMode: string;
    feverModeConf: { [key: string]: any };

    levelMemo: { [key: string]: { [key: string]: any } };
    hiddenRooms: {
        visited: { [key: string]: { [key: string]: boolean } };
        treasureOpened: { [key: string]: { [key: string]: boolean } };
        itemsCollected: { [key: string]: { [key: string]: boolean } };
    };
} = {
    removeAds: false,
    money: 2000,
    isPlaySound: true,
    isPlayBgMusic: true,
    warehouse: [],
    flyBoom: 0,
    downBoom: 0,
    boom: 1,
    ladder: 0,
    backpack: 3,
    isBuyFlyBoom: false,
    isBuyDownBoom: false,
    isBuyBoom: false,
    isBuyLadder: false,
    level: 1,
    currentLevelBoomDps: 0,
    boomScope: 1,
    userSkill: [],
    light: 750, // 750
    heart: 3, // 3
    power: 1, // 1
    userLocks: ["1"],
    iceLock: true,
    fireLock: true,
    useTicketAt: "",

    currentPetID: 1, // 当前宠物，默认-1
    petsStorage: {
        1: 1,
        2: 0,
        3: 0,
        4: 0,
    }, // 已解锁的宠物{宠物ID：级别默认0}
    petsFragments: { // 碎片数量
        1: 0,
        2: 100,
        3: 0,
        4: 0,
    },
    gearCards: [], // 获得的可装备的卡，对应GameConfig.gearCards
    equippedCards: [], // 已装备的卡，对应GameConfig.gearCards, 最多6张
    canEquipCardsNum: 1, // 当前可装备的卡牌数量，对应GameConfig.gearCards
    currentWeapon: 1,
    weaponsStorage: [1], // 可装备的武器，对应GameConfig.weapons，默认[1]
    diamonds: 1000, // 钻石数量
    keys: 0, // 钥匙数量
    grade: 1, // 等级
    gradeRatio: 0, // 升一级进度条比例：0-1
    currentExp: 0, // 当前经验值
    currentChapter: 1, // 当前章节 默认1
    lastChapter: 0, // 上一章，用作首页翻转，不翻转置为0
    currentLevel: 1, // 当前关卡 默认1
    lastLevel: 0, // 上一关，用作首页翻转，不翻转置为0
    currentLevelKeyCount: 0, // 获取到的当前关卡的钥匙数量
    currentLevelDiamondCount: 0, // 获取到的当前关卡的钻石数量
    currentLevelDirtTotalCount: 0, // 获取到的当前关卡的泥土总数量
    fragments: { // 碎片数量, 1-4对应绿蓝紫橙
        1: 0,
        2: 0,
        3: 0,
        4: 0,
    }, // 碎片数量
    selectedCard: 0, // 技能卡升级页面，当前选中的卡牌ID GameConfig.cards 默认0
    cards: [], // 用户所有技能卡 GameConfig.cards
    currentLevelupUnlocksIndex: -1, // 升级页面 GameConfig.levelupUnlocks 的索引，初始值为-1
    currentLevelupUnlocksSubIndex: -1, // 升级页面 GameConfig.levelupUnlocks中unlocks的索引，初始值为-1
    nextScene: null,
    nextSceneAction: null,
    currentLevelCallLadderCardID: -1, // 当前关卡使用了梯子卡ID
    currentLevelLampHalfCardID: -1, // 当前关卡使用了本局矿灯时间减半，但是获取的收益会翻倍
    currentLevelAddHalfCardID: -1, // 当前关卡如果完成任务，获得资源+50%
    currentLevelResurgenceCardID: -1, // 当前关卡使用了复活卡ID
    currentLevelAdDoubleCoinRatio: 1, // 当前关卡广告双倍收益次数, 默认1，看完 广告后为2
    joystickPos: "Left", // 当前摇杆位置，默认左侧 Left|Center|Right
    joystickSize: "M", // 当前摇杆大小，默认中号 S|M|L
    isFirstOpen: true, // 是否是第一次打开游戏
    hasFoundMines: [], // 已经找到的矿石,如[GameConfig.blockKinds.MINE_A20, MINE_A21...]
    footerTabsIsLock: {
        store: true,
        equip: true,
        level: false,
        upgrade: true,
        card: true,
    }, // 底部导航栏是否锁定，默认锁定
    commentCount: 0, // 评论的次数，默认0
    nextMode: "general", // "general" | "fever"
    feverModeConf: {
        date: "", // 默认""
        count: 3, // 默认3
    },

    // 生成过的层数数据，数组索引代表层数，内部数组表示对应tile的数据，
    // {"y:x": {tile: -1 | 'chapter1MapTopCenterFrame', block: {type: 'mine', kind: 1} | -1}}}
    levelMemo: {},

    // 隐藏房间相关数据
    hiddenRooms: {
        // 已访问过的隐藏房间 {"chapter:level": true}
        visited: {},
        // 宝箱开启状态 {"chapter:level": true}
        treasureOpened: {},
        // 房间道具收集状态 {"chapter:level": {"itemType:x:y": true}}
        itemsCollected: {}
    },
};

export const PropData = {
    ladder: 200,
    boom: 20,
    flyBoom: 10,
    downBoom: 10,
};
