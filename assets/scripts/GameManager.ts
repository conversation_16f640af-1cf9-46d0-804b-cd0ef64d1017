import {
    _decorator,
    Camera,
    Component,
    instantiate,
    Label,
    Node,
    Prefab,
    Sprite,
    SpriteFrame,
    tween,
    Vec3,
} from "cc";
import { EventBus } from "./EventBus";
import { UserData } from "./Utils/UserData";
import { BcakItem } from "./Backpack/BcakItem";
import { saveMoneyToWechat, saveUserData } from "./Utils/Tools";
import { coinCtl } from "./Game/coinCtl";
import { director } from "cc";
import { Player } from "./Player";
import { GameConfig } from "./GameConfig";
import { Chapters } from "./Chapters/Chapters";
import { ItemFloatUpCtl } from "./Game/ItemFloatUpCtl";
import { Map } from "./Map";
const { ccclass, property } = _decorator;

@ccclass("GameManager")
export class GameManager extends Component {
    // @property(Camera)
    // bgCamera: Camera;
    @property(Camera)
    mainCamera: Camera;
    @property(Camera)
    uiCamera: Camera;

    @property(Prefab)
    gameoverMaskFab: Prefab;

    @property(Prefab)
    public backpackItem: Prefab = null!;

    @property(Node)
    public backpack: Node = null!;
    @property(Node)
    public backpackTwo: Node = null!;

    @property(Node)
    public coinNode: Node = null!;

    @property(Prefab)
    public coinItem: Prefab = null!;
    @property(Prefab)
    public backpackS: Prefab = null!;

    @property({ type: SpriteFrame })
    public frame19: SpriteFrame = null;
    @property({ type: SpriteFrame })
    public frame20: SpriteFrame = null;
    @property({ type: SpriteFrame })
    public frame26: SpriteFrame = null;
    @property({ type: SpriteFrame })
    public frame31: SpriteFrame = null;
    @property({ type: SpriteFrame })
    public frame30: SpriteFrame = null;
    @property({ type: SpriteFrame })
    public frame25: SpriteFrame = null;
    @property({ type: SpriteFrame })
    public frame42: SpriteFrame = null;
    @property({ type: SpriteFrame })
    public frame45: SpriteFrame = null;
    @property({ type: SpriteFrame })
    public frame36: SpriteFrame = null;
    @property({ type: SpriteFrame })
    public frame28: SpriteFrame = null;
    @property({ type: SpriteFrame })
    public frame34: SpriteFrame = null;
    @property({ type: SpriteFrame })
    public frame48: SpriteFrame = null;
    @property({ type: SpriteFrame })
    public frame50: SpriteFrame = null;
    @property({ type: SpriteFrame })
    public frame46: SpriteFrame = null;
    @property({ type: SpriteFrame })
    public frame44: SpriteFrame = null;
    @property({ type: SpriteFrame })
    public frame24: SpriteFrame = null;
    @property({ type: SpriteFrame })
    public frame27: SpriteFrame = null;
    @property({ type: SpriteFrame })
    public frame35: SpriteFrame = null;
    @property({ type: SpriteFrame })
    public frame37: SpriteFrame = null;
    @property({ type: SpriteFrame })
    public frame38: SpriteFrame = null;

    @property({ type: SpriteFrame })
    public frame39: SpriteFrame = null;
    @property({ type: SpriteFrame })
    public frame41: SpriteFrame = null;

    @property(Prefab)
    public itemFloatUpPrefab: Prefab = null!;

    start() {
        // console.log(this.mainCamera.orthoHeight);
        this.uiCamera.orthoHeight = this.mainCamera.orthoHeight;

        EventBus.on("loadGameMask", this.loadGameMask, this);

        //TODO: 背包管理 UserData.backpack 数量
        const list = this.backpack.children;
        const listTwo = this.backpackTwo.children;
        const backpackList = [...list, ...listTwo];
        for (let i = 0; i < UserData.backpack; i++) {
            backpackList[i].active = true;
        }

        EventBus.on("backpackAdd", this.backpackAdd, this);
        EventBus.on("coinAdd", this.coinAdd, this);
        EventBus.on("settle", this.settle, this);
    }

    protected onDestroy(): void {
        EventBus.off("loadGameMask", this.loadGameMask, this);
        EventBus.off("backpackAdd", this.backpackAdd, this);
        EventBus.off("coinAdd", this.coinAdd, this);
        EventBus.off("settle", this.settle, this);
    }

    coinAdd(num: number, _coinNode: Node, playerNode: Node) {
        const coin = this.coinNode.getChildByName("Label").getComponent(Label);
        let count = parseInt(coin.string) + num;
        coin.string = count.toString();

        // 金币向上飘的效果
        let itemFloatUp = instantiate(this.itemFloatUpPrefab);
        itemFloatUp.getComponent(ItemFloatUpCtl).kind = "Coin";
        itemFloatUp.getComponent(ItemFloatUpCtl).count = num;
        playerNode.addChild(itemFloatUp);

        // 更新金币任务完成情况
        this.node.parent.getChildByPath("TiledMap/Player").getComponent(Player).updateCurrentLevelTargetData("coin", count);
    }

    backpackAdd(b: Node, bs: { kind: number; subKind: string }) {
        let list = this.backpack.children;
        let listTwo = this.backpackTwo.children;
        const tempList = [...list, ...listTwo];
        const backpackList = tempList.filter((item) => item.active);

        for (let i = 0; i < backpackList.length; i++) {
            const item = backpackList[i];
            const comp = item.getComponent(BcakItem);
            if (comp.oreName === bs.kind.toString()) {
                if (comp.count >= 99) {
                    tween(b)
                        .by(
                            0.3,
                            {
                                position: new Vec3(0, -64, 0),
                            },
                            {
                                onComplete: () => {
                                    b.destroy();
                                },
                            }
                        )
                        .start();
                    return;
                }
                comp.upTitle(1, this.getSpriteFrame(bs.kind.toString()));
                b.destroy();
                // this.flyToBackpack(b, bs);
                return;
            } else {
                if (comp.oreName === "") {
                    comp.oreName = bs.kind.toString();
                    b.destroy();
                    comp.upTitle(1, this.getSpriteFrame(bs.kind.toString()));
                    // this.flyToBackpack(b, bs);
                    return;
                }
            }
        }
        tween(b)
            .by(
                0.3,
                {
                    position: new Vec3(0, 64, 0),
                },
                {
                    onComplete: () => {
                        b.destroy();
                    },
                }
            )
            .start();
    }

    flyToBackpack(b: Node, bs: { kind: number; subKind: string }) {
        const root = this.backpack.parent;
        const coinB = instantiate(this.backpackS);
        coinB.getChildByName("Sprite").getComponent(Sprite).spriteFrame = this.getSpriteFrame(
            bs.kind.toString()
        );

        const pos = this.node.parent
            .getChildByName('TiledMap')
            .getComponent(Map)
            .convertChildPositionToUI(b);

        coinB.setPosition(pos);
        this.node.parent.getChildByName("UI").addChild(coinB);

        const localPos = root.getPosition();

        tween(coinB)
            .by(
                0.5,
                {
                    position: new Vec3(localPos.x, localPos.y+200, 0),
                },
                {
                    onComplete: () => {
                        coinB.destroy();
                    },
                }
            )
            .start();
    }

    loadGameMask() {
        let mask = instantiate(this.gameoverMaskFab);
        // this.node.parent.addChild(mask);
        director.getScene().getChildByPath("Canvas/GameoverMask").addChild(mask);
    }

    //根据name 获取对应的spriteFrame
    public getSpriteFrame(name: string) {
        switch (name) {
            case "10":
                return this.frame19;
            case "11":
                return this.frame20;
            case "12":
                return this.frame26;
            case "13":
                return this.frame31;
            case "14":
                return this.frame30;
            case "15":
                return this.frame25;
            case "16":
                return this.frame42;
            case "17":
                return this.frame45;
            case "21":
                return this.frame36;
            case "22":
                return this.frame28;
            case "23":
                return this.frame34;
            case "24":
                return this.frame48;
            case "25":
                return this.frame50;
            case "26":
                return this.frame46;
            case "27":
                return this.frame44;
            case "31":
                return this.frame24;
            case "32":
                return this.frame27;
            case "33":
                return this.frame35;
            case "34":
                return this.frame37;
            case "35":
                return this.frame38;
            case "36":
                return this.frame39;
            case "37":
                return this.frame41;
        }
    }

    //结算矿石和金币
    public settle(type: string, mode: string) {
        // let list = this.backpack.children;
        // let listTwo = this.backpackTwo.children;
        // const tempList = [...list, ...listTwo];
        // const backpackList = tempList.filter((item) => item.active);
        // const temp = [];


        // for (let i = 0; i < backpackList.length; i++) {
        //     const item = backpackList[i];
        //     const comp = item.getComponent(BcakItem);

        //     if (comp.oreName !== "") {
        //         temp.push({
        //             oreName: comp.oreName,
        //             count: comp.endCount,
        //         });
        //     }
        // }

        // 更新 UserData
        let coin = this.coinNode.getComponent(coinCtl).endCount;
        let userGrade = UserData.grade;
        if (type === "success") {
            // 自己挖的
            // -----coin-----
            // 当前关卡如果完成任务，获得资源+50%
            if (UserData.currentLevelAddHalfCardID !== -1) {
                coin = Math.floor(coin * GameConfig.cards[UserData.currentLevelAddHalfCardID].value1);
            }
            // 矿灯时间减半，但是获取的收益会翻倍
            if (UserData.currentLevelLampHalfCardID !== -1) {
                coin = Math.floor(coin * GameConfig.cards[UserData.currentLevelLampHalfCardID].value1);
            }
            // 看广告金币翻倍 默认值1 看完广告之后是2
            coin = coin * UserData.currentLevelAdDoubleCoinRatio;
            UserData.money += coin; // 挖掘发现的
            // -----diamond-----
            if (UserData.currentLevelAddHalfCardID !== -1) {
                // 当前关卡如果完成任务，获得资源+50%
                UserData.currentLevelDiamondCount = Math.floor(UserData.currentLevelDiamondCount * GameConfig.cards[UserData.currentLevelAddHalfCardID].value1);
            }
            // 矿灯时间减半，但是获取的收益会翻倍
            if (UserData.currentLevelLampHalfCardID !== -1) {
                UserData.currentLevelDiamondCount = Math.floor(UserData.currentLevelDiamondCount * GameConfig.cards[UserData.currentLevelLampHalfCardID].value1);
            }
            UserData.diamonds += UserData.currentLevelDiamondCount; // 挖掘发现的
            // -----key-----
            if (UserData.currentLevelAddHalfCardID !== -1) {
                // 当前关卡如果完成任务，获得资源+50%
                UserData.currentLevelKeyCount = Math.floor(UserData.currentLevelKeyCount * GameConfig.cards[UserData.currentLevelAddHalfCardID].value1);
            }
            // 矿灯时间减半，但是获取的收益会翻倍
            if (UserData.currentLevelLampHalfCardID !== -1) {
                UserData.currentLevelKeyCount = Math.floor(UserData.currentLevelKeyCount * GameConfig.cards[UserData.currentLevelLampHalfCardID].value1);
            }
            UserData.keys += UserData.currentLevelKeyCount; // 挖掘发现的
            // 通关奖励
            if (mode === "general") {
                let rewards = Chapters[UserData.currentChapter-1][UserData.currentLevel-1]["rewards"];
                for (let i=0; i < rewards.length;  i++) {
                    if (rewards[i]["kind"] === "coin") {
                        UserData.money += rewards[i]["count"];
                    } else if (rewards[i]["kind"] === "diamond") {
                        UserData.diamonds += rewards[i]["count"];
                    } else if (rewards[i]["kind"] === "key") {
                        UserData.keys += rewards[i]["count"];
                    } else if (rewards[i]["kind"] === "cardPart") {
                        let cardID = rewards[i]["id"];
                        let cardRare = GameConfig.cards[cardID]["rare"];
                        UserData.fragments[cardRare] += rewards[i]["count"]
                    } else if (rewards[i]["kind"] === "card") {
                        UserData.cards.push(rewards[i]["id"])
                        UserData.selectedCard = rewards[i]["id"]
                    } else if (rewards[i]["kind"] === "exp") {
                        UserData.currentExp += rewards[i]["count"];

                        // 根据经验值更新用户等级
                        // 当前等级 +10级遍历符合哪个等级的经验值
                        for (let i = 0; i < 10; i++) {
                            let exp = GameConfig.levelupUnlocks[UserData.grade - 1 + i].exp;
                            let nextExps = GameConfig.levelupUnlocks[UserData.grade + i].exp;

                            if (UserData.currentExp >= exp && UserData.currentExp < nextExps) {
                                UserData.grade += i;
                                let der = (nextExps - exp)
                                UserData.gradeRatio = Number.parseFloat((1-(nextExps - UserData.currentExp) / der).toFixed(2));
                                break;
                            }
                        }
                    }
                }

                // 进到下一级
                if (Chapters[UserData.currentChapter-1][UserData.currentLevel] === undefined) {
                    UserData.lastChapter = UserData.currentChapter;
                    UserData.currentChapter++;
                    UserData.currentLevel = 1;
                } else {
                    UserData.lastLevel = UserData.currentLevel;
                    UserData.currentLevel++;
                }
            }
        } else if (type === "gameover") {
            // 挖掘发现的

            // 使用了CALL_LADDER卡片
            if (UserData.currentLevelCallLadderCardID !== -1) {
                coin = Math.floor(coin * (1-GameConfig.cards[UserData.currentLevelCallLadderCardID].value1));
                UserData.currentLevelDiamondCount = Math.floor(UserData.currentLevelDiamondCount * (1-GameConfig.cards[UserData.currentLevelCallLadderCardID].value1));
                UserData.currentLevelKeyCount = Math.floor(UserData.currentLevelKeyCount * (1-GameConfig.cards[UserData.currentLevelCallLadderCardID].value1));
            }

            // 矿灯时间减半，但是获取的收益会翻倍
            if (UserData.currentLevelLampHalfCardID !== -1) {
                coin = Math.floor(coin * GameConfig.cards[UserData.currentLevelLampHalfCardID].value1);
                UserData.currentLevelDiamondCount = Math.floor(UserData.currentLevelDiamondCount * GameConfig.cards[UserData.currentLevelLampHalfCardID].value1);
                UserData.currentLevelKeyCount = Math.floor(UserData.currentLevelKeyCount * GameConfig.cards[UserData.currentLevelLampHalfCardID].value1);
            }

            // 看广告金币翻倍 默认值1 看完广告之后是2
            coin = coin * UserData.currentLevelAdDoubleCoinRatio;

            // 根据经验值更新用户等级
            // 当前等级 +10级遍历符合哪个等级的经验值
            if (mode === "general") {
                let exp = Math.floor(UserData.currentLevelDirtTotalCount * 0.05);
                if (exp > 0) {
                    UserData.currentExp += exp;
                    for (let i = 0; i < 10; i++) {
                        let exp = GameConfig.levelupUnlocks[UserData.grade - 1 + i].exp;
                        let nextExps = GameConfig.levelupUnlocks[UserData.grade + i].exp;

                        if (UserData.currentExp >= exp && UserData.currentExp < nextExps) {
                            UserData.grade += i;
                            let der = (nextExps - exp)
                            UserData.gradeRatio = Number.parseFloat((1-(nextExps - UserData.currentExp) / der).toFixed(2));
                            break;
                        }
                    }
                }
            }

            UserData.money += coin;
            UserData.diamonds += UserData.currentLevelDiamondCount;
            UserData.keys += UserData.currentLevelKeyCount;
        }

        if (userGrade !== UserData.grade) {
            UserData.nextMode = "fever"; // 升级直接跳转到fever模式
        } else {
            UserData.nextMode = "general";
        }

        // 清空
        this.clearCurrentLevelUserData();
        // const tempWare = [...UserData.warehouse];

        // for (let i = 0; i < temp.length; i++) {
        //     const tempI = temp[i];
        //     const index = tempWare.findIndex((k) => k.name === tempI.oreName);
        //     if (index >= 0) {
        //         tempWare[index].count += tempI.count;
        //     } else {
        //         tempWare.push({
        //             name: tempI.oreName,
        //             count: tempI.count,
        //         });
        //     }
        // }
        // UserData.warehouse = tempWare;

        console.log("xxxxmoney", UserData.money);
        console.log("xxxxdiamonds", UserData.diamonds);
        console.log("xxxxkeys", UserData.keys);
        console.log("xxxxfragments", UserData.fragments);
        console.log("xxxxcards", UserData.cards);
        console.log("xxxxcurrentExp", UserData.currentExp);
        console.log("xxxxgradeRatio", UserData.gradeRatio);
        saveUserData();
        saveMoneyToWechat();
    }

    clearCurrentLevelUserData() {
        UserData.currentLevelBoomDps = 0;

        UserData.currentLevelDiamondCount = 0;
        UserData.currentLevelKeyCount = 0;
        UserData.currentLevelCallLadderCardID = -1;
        UserData.currentLevelLampHalfCardID = -1;
        UserData.currentLevelAddHalfCardID = -1;
        UserData.currentLevelAdDoubleCoinRatio = 1;
        UserData.currentLevelDirtTotalCount = 0;
    }

}
